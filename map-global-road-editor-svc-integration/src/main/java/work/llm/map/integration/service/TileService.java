package work.llm.map.integration.service;

import cn.huolala.arch.hermes.compatible.util.http.HttpResult;
import cn.huolala.arch.hermes.compatible.util.http.HttpUtils;
import cn.lalaframework.config.core.PropertyConfigurer;
import cn.lalaframework.model.Result;
import cn.lalaframework.utils.JsonUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class TileService {


    public String getTileId(String geometry) throws Exception {
        return this.getTileId(7, geometry);
    }

    @SuppressWarnings("unchecked")
    public String getTileId(Integer resolution, String geometry) throws Exception {
        String url = PropertyConfigurer.getString("inner.client.url.tile-service");

        Map<String, String> param = new HashMap<>();
        param.put("resolution", String.valueOf(resolution));
        param.put("geometry", geometry);

        HttpResult httpResult = HttpUtils.doGet(url, param);
        Result<String> result = JsonUtils.string2Obj(httpResult.getBody(), Result.class);
        return result.getData();
    }
}
