package work.llm.map.integration.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class AuthToken {

	@JsonProperty(value = "user_id")
	private Integer userId;

	@JsonProperty(value = "access_token")
	private String accessToken;

	@JsonProperty(value = "expires_in")
	private String expiresIn;

	@JsonProperty(value = "user_fid")
	private String userFid;

	@JsonProperty(value = "validity_second")
	private Long validitySecond;
	private Long aid;

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public String getExpiresIn() {
		return expiresIn;
	}

	public void setExpiresIn(String expiresIn) {
		this.expiresIn = expiresIn;
	}

	public String getUserFid() {
		return userFid;
	}

	public void setUserFid(String userFid) {
		this.userFid = userFid;
	}

	public Long getValiditySecond() {
		return validitySecond;
	}

	public void setValiditySecond(Long validitySecond) {
		this.validitySecond = validitySecond;
	}

	public Long getAid() {
		return aid;
	}

	public void setAid(Long aid) {
		this.aid = aid;
	}

}
