package work.llm.map.integration.service;

import cn.huolala.arch.hermes.compatible.util.http.HttpResult;
import cn.huolala.arch.hermes.compatible.util.http.HttpUtils;
import cn.lalaframework.config.core.PropertyConfigurer;
import cn.lalaframework.utils.JsonUtils;
import org.springframework.stereotype.Service;
import work.llm.map.integration.model.IdParam;

import java.util.List;

@Service
public class IdService {

    @SuppressWarnings("all")
    public List<Long> generateIds(String datasource, int size) throws Exception {
        if (datasource.contains(",")) {
            String[] sources = datasource.split(",");
            datasource = sources[sources.length - 1];
        }
        IdParam param = new IdParam();
        param.setIdSource(Long.valueOf(datasource));
        param.setSize((long) size);

        String url = PropertyConfigurer.getString("inner.client.url.id-service");

        HttpResult httpResult = HttpUtils.doPost(url, JsonUtils.obj2String(param));
        return JsonUtils.string2Obj(httpResult.getBody(), List.class);
    }
}
