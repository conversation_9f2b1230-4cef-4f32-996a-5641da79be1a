package work.llm.map.integration.service;

import java.util.Map;
import work.llm.map.integration.model.AuthParam;
import work.llm.map.integration.model.AuthToken;
import cn.huolala.arch.hermes.api.annotation.Method;
import cn.huolala.arch.hermes.api.annotation.Param;
import cn.huolala.arch.hermes.api.http.HttpMethod;
import cn.huolala.arch.hermes.api.http.ParamsMode;
import cn.huolala.arch.hermes.api.http.ResultMode;

/**
 * <AUTHOR>
 * 第三方服务接口
 * 泛化调用服务定义
 * 泛化调用方会扫码@SOAService来调用服务
 * 具体泛化协议识别请参考 https://wiki.huolala.work/pages/viewpage.action?pageId=********
 */
public interface TokenService {

	/**
	 * getToken2
	 * @param param
	 * @return
	 */
	@Method(value = "name=account.login.verify", http= @HttpMethod(paramsMode = ParamsMode.BODY_JSON, resultMode = ResultMode.STRING))
	public String getToken2(Map<String, Object> param); //Map转JSON

	/**
	 * getToken3
	 * @param appId
	 * @param channelId
	 * @param version
	 * @param data
	 * @return
	 */
	@Method(value = "name=account.login.verify", http= @HttpMethod(paramsMode = ParamsMode.BODY_JSON, resultMode = ResultMode.STRING))
	public String getToken3(@Param("app_id") String appId, @Param("channel_id") String channelId,
							@Param("version") String version, @Param("data") String data); //参数转JSON

	/**
	 * getToken4
	 * @param param
	 * @return
	 */
	@Method(value = "name=account.login.verify", http= @HttpMethod(paramsMode = ParamsMode.BODY_JSON, resultMode = ResultMode.OBJECT))
	public AuthToken getToken4(AuthParam param);

	/**
	 * getToken5
	 * @param param
	 * @return
	 */
	@Method(value = "name=account.login.verify", http= @HttpMethod(paramsMode = ParamsMode.BODY_JSON, resultMode = ResultMode.OBJECT))
	public AuthToken getToken5(Map<String, Object> param);

}
