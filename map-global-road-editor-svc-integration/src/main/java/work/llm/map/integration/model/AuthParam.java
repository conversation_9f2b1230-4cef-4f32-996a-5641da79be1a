package work.llm.map.integration.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 默认的结果封装类.
 *
 * <AUTHOR>
 *
 */
public class AuthParam {

	@JsonProperty(value = "app_id")
	private String appId;

	@JsonProperty(value = "channel_id")
	private String channelId;

	private String version;

	private String data;

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getData() {
		return data;
	}

	public void setData(String data) {
		this.data = data;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getChannelId() {
		return channelId;
	}

	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}
}