package work.llm.map;

import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 测试服务基类 需要启动mvc的web容器以提供ServletContext
 *
 **/
@Ignore
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = SpringServiceTestApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class BaseTest {
    @BeforeClass
    public static void before(){
        System.setProperty("hll.app.id", "demo");
        System.setProperty("region", "2");
        System.setProperty("hll.env", "stg");
    }
}
