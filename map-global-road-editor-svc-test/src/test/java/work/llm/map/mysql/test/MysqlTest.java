package work.llm.map.mysql.test;

import work.llm.map.demo.mysql.service.MysqlService;
import work.llm.map.BaseTest;
import work.llm.map.service.user.model.UserInfoDO;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class MysqlTest extends BaseTest {

    @Autowired
    private MysqlService mysqlService;

    private static final Long ID =1L;

    @Test
    public void dynamicTest() throws Exception {

        mysqlService.findById(ID);
        int total = mysqlService.getPageCount();
        if (total > 0) {
            List<UserInfoDO> list =  mysqlService.getPageList(1, 5);
            Assert.assertTrue(list.size() > 0);
        }

        mysqlService.updateDb1(ID);
        mysqlService.updateDb2(ID);
        mysqlService.update(ID, ID);
    }
}
