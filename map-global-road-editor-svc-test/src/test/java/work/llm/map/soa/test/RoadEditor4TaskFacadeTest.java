package work.llm.map.soa.test;

import cn.huolala.arch.hermes.api.annotation.HermesReference;
import org.junit.Assert;
import org.junit.Test;
import work.llm.map.BaseTest;
import work.llm.map.facade.model.TaskCreateDTO;
import work.llm.map.facade.service.RoadEditor4TaskFacade;
// import work.llm.map.provider.RoadEditor4Task4TaskProvider;

import javax.annotation.Resource;

public class RoadEditor4TaskFacadeTest extends BaseTest {

    // 方式1：通过Hermes RPC调用（模拟远程调用）
    @HermesReference("ci-test-svc")
    private RoadEditor4TaskFacade roadEditor4TaskFacade;

    // 方式2：直接注入Provider（本地调用，更快调试）
    // @Resource
    // private RoadEditor4Task4TaskProvider roadEditor4TaskProvider;

    @Test
    public void testCreateTaskViaRpc() {
        TaskCreateDTO taskCreateDTO = new TaskCreateDTO();
        // 设置你的测试数据
        // taskCreateDTO.setXxx(...);
        
        boolean result = roadEditor4TaskFacade.createTask(taskCreateDTO);
        System.out.println("RPC Create task result: " + result);
        Assert.assertTrue(result);
    }

    @Test
    public void testCreateTaskDirect() {
        TaskCreateDTO taskCreateDTO = new TaskCreateDTO();
        // 设置你的测试数据
        // taskCreateDTO.setXxx(...);
        
        // boolean result = roadEditor4TaskProvider.createTask(taskCreateDTO);
        // System.out.println("Direct Create task result: " + result);
        // Assert.assertTrue(result);
    }
}