package work.llm.map.soa.test;

import cn.huolala.arch.hermes.api.annotation.HermesReference;
import org.junit.Assert;
import org.junit.Test;
import work.llm.map.BaseTest;
import work.llm.map.dao.link.entity.Link;
import work.llm.map.facade.model.UserDTO;
import work.llm.map.facade.model.UserPage;
import work.llm.map.facade.service.DemoUserStandardFacade;
import work.llm.map.service.common.CommonService;
import work.llm.map.service.common.PathCalculator;
import work.llm.map.service.link.LinkService;

import javax.annotation.Resource;
import java.util.*;


public class DemoSoaStandardServiceTest extends BaseTest {

    @HermesReference("ci-test-svc")
    private DemoUserStandardFacade demoUserStandardFacade;
    @Resource
    private CommonService commonService;
    @Resource
    private LinkService linkService;

    @Test
    public void soaProviderTest() throws Exception {
        UserDTO userDTO = new UserDTO();
        userDTO.setAge(50);
        userDTO.setName("test500");
        demoUserStandardFacade.createUser(userDTO);

        UserDTO userDTO2 = demoUserStandardFacade.getById(1L);
        Assert.assertTrue(Objects.nonNull(userDTO2));

        UserPage result = demoUserStandardFacade.getUserPage(1, 5);
        Assert.assertTrue(result.getTotal() > 0);
    }

    @Test
    public void testRuleInfo() {
        // Link inLink = linkService.lambdaQuery().eq(Link::getHllLinkid, "864691129692915807").one();
        // Link outLink = linkService.lambdaQuery().eq(Link::getHllLinkid, "864691129672960654").one();
        // Link inLinkNext = linkService.lambdaQuery().eq(Link::getHllLinkid, ).one();
        // Link outLinkBefore = linkService.lambdaQuery().eq(Link::getHllLinkid, ).one();


        String inlinkId = "864691129207186624";
        String outlinkId = "864691129207004364";

        Map<String, Link> linkMap = null;
        List<String> pathLinkids;
        if (!inlinkId.equals(outlinkId)) {
            PathCalculator calculator = new PathCalculator(inlinkId, outlinkId);
            pathLinkids = calculator.ruleComplete();
            linkMap = calculator.getLinkMap();
        } else {
            pathLinkids = new ArrayList<>(Collections.singletonList(inlinkId));
        }

        if (!inlinkId.equals(outlinkId)) {
            Link inLink = linkMap.get(inlinkId);
            Link inLinkNext = linkMap.get(pathLinkids.get(1));
            Link outLink = linkMap.get(outlinkId);
            Link outLinkBefore = linkMap.get(pathLinkids.get(pathLinkids.size() - 2));

            int ruleInfo = commonService.ruleInfo(inLink, outLink, inLinkNext, outLinkBefore);
        }

    }
}
