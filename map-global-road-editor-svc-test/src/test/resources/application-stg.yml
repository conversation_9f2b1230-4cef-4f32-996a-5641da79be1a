spring:
  redis:
    host: *************
    port: 16379
    password: hll_map_road@2021
  datasource:
    dynamic:
      enabled: true
      primary: default
      datasource:
        default:
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://*************:15999/oversea_road_editor_base
          username: postgres
          password: <PERSON><PERSON><PERSON>@2021
        task:
          url: jdbc:postgresql://*************:15999/global_road_task_stg
          driver-class-name: org.postgresql.Driver
          username: postgres
          password: <PERSON><PERSON><PERSON>@2021
        mys:
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://*************:15999/hll_oversea_h_mys_2025_q2
          username: postgres
          password: <PERSON><PERSON><PERSON>@2021
        phl:
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://*************:15999/hll_oversea_h_phl_2025_q2
          username: postgres
          password: <PERSON><PERSON><PERSON>@2021
        sgp:
          driver-class-name: org.postgresql.Driver
          url: ***************************************************************
          username: postgres
          password: Huolala@2021
        hkg:
          driver-class-name: org.postgresql.Driver
          url: ***************************************************************
          username: postgres
          password: Huolala@2021
        idn:
          driver-class-name: org.postgresql.Driver
          url: ***************************************************************
          username: postgres
          password: Huolala@2021
        twn:
          driver-class-name: org.postgresql.Driver
          url: ***************************************************************
          username: postgres
          password: Huolala@2021

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl