#如有组件方面的使用疑问请参考 https://micro.huolala.work/doc/#/ldeps/doc
#在本地调试请参考https://huolala.feishu.cn/wiki/wikcnkzvZNO3iDAjXgVJhMPWQlg
apollo:
  #apollo中 项目app id下application,consul配置文件 
  #新建的项目 在apollo中无该配置 需进行申请
  bootstrap:
    namespaces: application

server:
  servlet:
    application-display-name: ${spring.application.name}
    context-path: /api/oversea/road/editor
  port: 8888

logging:
  level:
    root: info
    cn.huolala.bme: info

mybatis-plus:
  mapper-locations: classpath*:mybatis/mapper/*.xml
  type-aliases-package: work.llm.map.dao.**.entity*
  type-aliases-super-type: cn.lalaframework.orm.mybatis.entity.PO
  type-handlers-package: work.llm.map.dao.common
  global-config:
    db-config:
      update-strategy: ignored
  configuration:
    lazy-loading-enabled: false
    map-underscore-to-camel-case: true
    default-fetch-size: 100
    default-statement-timeout: 3

spring:
  #  #datasource config start
  #  datasource:
  #    dynamic:
  #      primary: db1
  #      datasource:
  #        db1:
  #          ci-resource-id: mysql.ci_jaf_demo #db资源ID，在lalaplat中申请
  #          driver-class-name: com.mysql.jdbc.Driver
  #          #type: com.zaxxer.hikari.HikariDataSource
  #        db2:
  #          ci-resource-id: mysql.ci_jaf_demo
  #          driver-class-name: com.mysql.jdbc.Driver
  #          #type: com.zaxxer.hikari.HikariDataSource
  #          #datasource config stop

  main:
    allow-bean-definition-overriding: true
  profiles:
    active: stg


lala:
  soa:
    hostPorts: #指定提供方{appId}的host和port，以方便在dev环境直连服务提供者进行调试,
      #以下配置需要指定启动参数-Dhll.env=dev才会生效
      ci-test-svc:
        host: 127.0.0.1
        port: 8082

properties:
  area-interceptor:
    header-key-area: x-oversea-area
    ignore-uris:
      - /springdoc/**
      - /area/**
      - /swagger-ui/**
      - /v3/api-docs/**
