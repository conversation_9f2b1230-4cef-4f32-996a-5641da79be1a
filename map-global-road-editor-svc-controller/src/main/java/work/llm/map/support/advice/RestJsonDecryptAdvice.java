package work.llm.map.support.advice;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;
import work.llm.map.core.toolkit.EnDecryptKit;
import work.llm.map.common.annotation.SkipDecrypt;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

@Slf4j
@ControllerAdvice
@SuppressWarnings("all")
public class RestJsonDecryptAdvice implements RequestBodyAdvice {

    // 判断哪些请求需要被处理
    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 优先检查注解
        if (methodParameter.hasMethodAnnotation(JsonWrapperIgnore.class) 
            || methodParameter.hasMethodAnnotation(SkipDecrypt.class)) {
            return false;
        }
        
        // 检查类级别注解
        Class<?> declaringClass = methodParameter.getDeclaringClass();
        if (declaringClass.isAnnotationPresent(JsonWrapperIgnore.class)
            || declaringClass.isAnnotationPresent(SkipDecrypt.class)) {
            return false;
        }
        
        String className = declaringClass.getName();
        
        // 如果是 provider 包下的类（外部服务实现），跳过解密
        if (className.contains(".provider.")) {
            return false;
        }
        
        // 也可以通过请求路径判断（需要从请求上下文获取）
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            String requestUrl = requestAttributes.getRequest().getRequestURI();
            // 如果是外部服务接口路径，跳过解密（根据你的实际路径规则调整）
            if (requestUrl.contains("/external/") || requestUrl.contains("/api/v1/")) {
                return false;
            }
        }
        
        return true;
    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) throws IOException {
        return decryptHttpInputMessage(inputMessage);
    }

    @Override
    public Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return body;
    }

    @Override
    public Object handleEmptyBody(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return body;
    }

    // 解密输入流
    private HttpInputMessage decryptHttpInputMessage(HttpInputMessage inputMessage) throws IOException {
        String encryptedBody = StreamUtils.copyToString(inputMessage.getBody(), StandardCharsets.UTF_8);

        // Step 1: 解密得到明文字符串
        String decryptedJson = decrypt(encryptedBody);  // 调用你的解密方法

        // 获取请求URL
        String requestUrl = "";
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            requestUrl = requestAttributes.getRequest().getRequestURI();
        }

        // 打印解密后的参数和请求URL
        log.info("Request URL: {}, Decrypted Parameters: {}", requestUrl, decryptedJson);

        // Step 2: 返回一个新的 HttpInputMessage，替换原始 body
        final ByteArrayInputStream decryptedStream = new ByteArrayInputStream(decryptedJson.getBytes(StandardCharsets.UTF_8));
        return new HttpInputMessage() {
            @Override
            public InputStream getBody() throws IOException {
                return decryptedStream;
            }

            @Override
            public HttpHeaders getHeaders() {
                return inputMessage.getHeaders();
            }
        };
    }

    // 示例：假设这是一个 AES/SM4 解密函数
    private String decrypt(String cipherText) {
        // 实际调用你的解密逻辑
        // 如：AESUtil.decrypt(cipherText, key);
        EnDecryptKit decryptKit = new EnDecryptKit(true);
        return decryptKit.decrypt(cipherText);
    }
}
