package work.llm.map.support.advice;

import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.lalaframework.exception.LalaException;
import cn.lalaframework.soa.jsonrpc.JsonRpcClientException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import work.llm.map.common.exception.BizErrorCode;
import work.llm.map.common.result.Result;
import work.llm.map.core.toolkit.EnDecryptKit;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Objects;
import java.util.Optional;

/**
 * global exception handler
 *
 * <AUTHOR>
 * @date 2021/12/8
 */
@Slf4j
@ControllerAdvice
@SuppressWarnings("rawtypes")
public class GlobalExceptionAdvice {


    private final static EnDecryptKit encryptKit = new EnDecryptKit(true);
    private final static ObjectMapper objectMapper = new ObjectMapper();

    private String encrypt(Result result) {
        try {
            return encryptKit.encrypt(objectMapper.writeValueAsString(result));
        } catch (JsonProcessingException e) {
            return "{\"ret\":\"20001\",\"msg\":\"Data Encryption Exception\",\"data\":null}";
        }
    }

    /**
     * BindException
     *
     * @param ex {@link BindException}
     * @return {@link Result}
     */
    @ResponseBody
    @ExceptionHandler(BindException.class)
    public String handle(BindException ex) {
        log.warn(ex.getMessage(), ex);

        Integer ret = BizErrorCode.BIND_FAIL.getRet();
        FieldError fieldError = ex.getBindingResult().getFieldError();
        String msg;
        if (fieldError == null || StringUtils.isEmpty(fieldError.getDefaultMessage())) {
            msg = BizErrorCode.BIND_FAIL.getMsg();
        } else {
            String defaultMsg = fieldError.getDefaultMessage();
            if (Objects.nonNull(defaultMsg) && defaultMsg.contains(IllegalArgumentException.class.getName())) {
                defaultMsg = defaultMsg.substring(defaultMsg.lastIndexOf(":") + 2);
            }
            msg = defaultMsg;
        }
        return encrypt(Result.fail(ret, msg));
    }

    /**
     * MethodArgumentNotValidException
     *
     * @param ex {@link MethodArgumentNotValidException}
     * @return {@link Result}
     */
    @ResponseBody
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public String handle(MethodArgumentNotValidException ex) {
        log.warn(ex.getMessage(), ex);

        Result result = Result.fail(BizErrorCode.VALIDATE_FAIL);

        FieldError fieldError = ex.getBindingResult().getFieldError();
        if (fieldError != null) {
            result.setMsg(fieldError.getDefaultMessage());
        }
        return encrypt(result);
    }

    /**
     * ConstraintViolationException
     *
     * @param ex {@link ConstraintViolationException}
     * @return {@link Result}
     */
    @ResponseBody
    @ExceptionHandler(ConstraintViolationException.class)
    public String handle(ConstraintViolationException ex) {
        log.warn(ex.getMessage(), ex);

        Result result = Result.fail(BizErrorCode.VALIDATE_FAIL);

        Optional<ConstraintViolation<?>> error = ex.getConstraintViolations().stream().findFirst();
        error.ifPresent(p -> result.setMsg(error.get().getMessage()));
        return encrypt(result);
    }

    /**
     * IllegalArgumentException
     *
     * @param ex {@link IllegalArgumentException}
     * @return {@link Result}
     */
    @ResponseBody
    @ExceptionHandler(IllegalArgumentException.class)
    public String handle(IllegalArgumentException ex) {
        log.warn(ex.getMessage(), ex);
        return encrypt(Result.fail(BizErrorCode.VALIDATE_FAIL));
    }

    /**
     * LalaException
     *
     * @param ex {@link LalaException}
     * @return {@link Result}
     */
    @ResponseBody
    @ExceptionHandler(LalaException.class)
    public String handle(LalaException ex) {
        log.error(ex.getMessage(), ex);

        if (ex.getRet() == null) {
            ex.setRet(BizErrorCode.SYSTEM_ERROR.getRet());
        }
        return encrypt(Result.fail(ex));
    }

    @ResponseBody
    @ExceptionHandler(JsonRpcClientException.class)
    public String handle(JsonRpcClientException ex) {
        log.error(ex.getMessage(), ex);

        int ret = ex.getData().get("ret").asInt(-1);
        String message = ex.getData().get("message").asText();
        return encrypt(Result.fail(ret, message));
    }

    @ResponseBody
    @ExceptionHandler(RpcException.class)
    public String handle(RpcException ex) {
        log.error(ex.getMessage(), ex);


        int ret = ex.getCode();
        String message = ex.getMessage();
        return encrypt(Result.fail(ret, message));
    }

    /**
     * 其他异常处理
     */
    @ResponseBody
    @ExceptionHandler(Exception.class)
    public String handle(Exception ex) {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        log.error("path:{},error:{}", requestAttributes == null ? "" : requestAttributes.getRequest().getRequestURI(), ex.getMessage(), ex);
        return encrypt(Result.fail(BizErrorCode.SYSTEM_ERROR));
    }
}
