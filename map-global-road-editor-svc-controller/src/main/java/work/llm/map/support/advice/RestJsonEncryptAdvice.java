package work.llm.map.support.advice;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;
import work.llm.map.common.result.Result;
import work.llm.map.core.toolkit.EnDecryptKit;

/**
 * wrapper json result with {@link Result}
 *
 * <AUTHOR>
 * @date 2021/12/8
 */
@RestControllerAdvice
public class RestJsonEncryptAdvice implements ResponseBodyAdvice<Object> {

    private static final String WRAPPER_PACKAGES = "work.llm.map.api";

    /**
     * Whether this component supports the given controller method return type and the selected {@code HttpMessageConverter} type.
     *
     * @param returnType    the return type
     * @param converterType the selected converter type
     * @return {@code true} if {@link #beforeBodyWrite} should be invoked; {@code false} otherwise
     */
    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        if (returnType.getMethodAnnotation(JsonWrapperIgnore.class) != null || returnType.getContainingClass().isAnnotationPresent(JsonWrapperIgnore.class)) {
            return false;
        }

        return returnType.getDeclaringClass().getName().startsWith(WRAPPER_PACKAGES);
    }

    /**
     * Invoked after an {@code HttpMessageConverter} is selected and just before its write method is invoked.
     *
     * @param body                  the body to be written
     * @param returnType            the return type of the controller method
     * @param selectedContentType   the content type selected through content negotiation
     * @param selectedConverterType the converter type selected to write to the response
     * @param request               the current request
     * @param response              the current response
     * @return the body that was passed in or a modified (possibly new) instance
     */
    @SneakyThrows
    @Override
    public Object beforeBodyWrite(
            Object body,
            MethodParameter returnType,
            MediaType selectedContentType,
            Class<? extends HttpMessageConverter<?>> selectedConverterType,
            ServerHttpRequest request,
            ServerHttpResponse response) {

        EnDecryptKit encryptKit = new EnDecryptKit(true);
        ObjectMapper mapper = new ObjectMapper();
        if (selectedConverterType.isAssignableFrom(StringHttpMessageConverter.class)) {
            String value = mapper.writeValueAsString(Result.success(body));
            return encryptKit.encrypt(value);
        }
        if (body instanceof Result) {
            String value = mapper.writeValueAsString(body);
            return encryptKit.encrypt(value);
        }
        String value = mapper.writeValueAsString(Result.success(body));
        return encryptKit.encrypt(value);
    }
}
