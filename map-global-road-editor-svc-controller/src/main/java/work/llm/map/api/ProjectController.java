package work.llm.map.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Tag(name = "项目API", description = "项目配置API")
@RestController
@RequestMapping("/project")
public class ProjectController {

    /* 常规环境：
    EDIT_WORKER: 1011,
    QUALITY_CHECKER: 1012,
    VERIFIER: 1013,
    天级环境：
    EDIT_WORKER: 11,
    QUALITY_CHECKER: 12,
    VERIFIER: 13*/
    // 分别初始化到map中
    private static final Map<String, Integer> PLATFORM_MAP = new HashMap<>();
    @Value("${spring.profiles.active}")
    private String profiles;

    @PostConstruct
    public void init() {
        PLATFORM_MAP.put("EDIT_WORKER", 1011);
        PLATFORM_MAP.put("QUALITY_CHECKER", 1012);
        PLATFORM_MAP.put("VERIFIER", 1013);
        if (profiles.contains("fast")) {
            PLATFORM_MAP.put("EDIT_WORKER", 11);
            PLATFORM_MAP.put("QUALITY_CHECKER", 12);
            PLATFORM_MAP.put("VERIFIER", 13);
        }
    }

    @GetMapping("/v1/platform")
    @Operation(summary = "platform接口", tags = "2.3")
    public Map<String, Integer> platform() {
        return PLATFORM_MAP;
    }
}
