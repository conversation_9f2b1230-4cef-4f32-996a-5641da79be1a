package work.llm.map.api.relation;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import work.llm.map.common.constant.Const;
import work.llm.map.core.aop.AreaSelector;
import work.llm.map.dao.relation.entity.Relation;
import work.llm.map.service.relation.RelationService;
import work.llm.map.service.relation.model.req.RelationAttributeReq;
import work.llm.map.service.relation.model.req.RelationDeleteReq;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "道路关系API", description = "道路关系相关接口API")
@RestController
@RequestMapping("/relation")
public class RelationController {

    @Resource
    private RelationService relationService;

    @AreaSelector
    @GetMapping("/v1/query/id")
    @Operation(summary = "根据[relationId]查询道路关系", tags = "2.0")
    public Relation queryById(@RequestParam String relationId,
                              @RequestParam(required = false, defaultValue = Const.DEFAULT_QUERY_STATUS) String status) {
        return relationService.queryById(relationId, status);
    }

    @AreaSelector
    @GetMapping("/v1/query/extent")
    @Operation(summary = "根据[extent]查询道路关系", tags = "2.0")
    public List<Relation> queryByMat(@RequestParam String extent,
                                     @RequestParam(required = false, defaultValue = Const.DEFAULT_QUERY_STATUS) String status) {
        return relationService.queryByExtent(extent, status);
    }

    @AreaSelector
    @PostMapping("/v1/add")
    @Operation(summary = "添加道路关系", tags = "2.0")
    public Relation add(@RequestBody RelationAttributeReq req) {
        return relationService.add(req);
    }

    @AreaSelector
    @PostMapping("/v1/update")
    @Operation(summary = "更新道路关系", tags = "2.0")
    public Relation update(@RequestBody RelationAttributeReq req) {
        return relationService.update(req);
    }

    @AreaSelector
    @PostMapping("/v1/delete")
    @Operation(summary = "删除道路关系", tags = "2.0")
    public Boolean update(@RequestBody RelationDeleteReq req) {
        return relationService.update(req);
    }
}
