package work.llm.map.api.rule;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import work.llm.map.common.constant.Const;
import work.llm.map.core.aop.AreaSelector;
import work.llm.map.dao.rule.entity.Rule;
import work.llm.map.service.common.LinkCalculateReq;
import work.llm.map.service.rule.RuleService;
import work.llm.map.service.rule.model.req.RuleAttributeReq;
import work.llm.map.service.rule.model.req.RuleDeleteReq;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "交限API", description = "交限相关接口API")
@RestController
@RequestMapping("/rule")
public class RuleController {

    @Resource
    private RuleService ruleService;

    @AreaSelector
    @GetMapping("/v1/query/id")
    @Operation(summary = "根据[ruleId]查询道路交限", tags = "2.0")
    public Rule queryById(@RequestParam String ruleId,
                          @RequestParam(required = false, defaultValue = Const.DEFAULT_QUERY_STATUS) String status) {
        return ruleService.queryById(ruleId, status);
    }

    @AreaSelector
    @GetMapping("/v1/query/extent")
    @Operation(summary = "根据[extent]查询道路交限", tags = "2.0")
    public List<Rule> queryByExtent(@RequestParam String extent,
                                 @RequestParam(required = false, defaultValue = Const.DEFAULT_QUERY_STATUS) String status) {
        return ruleService.queryByExtent(extent, status);
    }

    @AreaSelector
    @PostMapping("/v1/calculate")
    @Operation(summary = "计算道路交限", tags = "2.0")
    public Rule add(@RequestBody LinkCalculateReq req) {
        return ruleService.calculate(req);
    }

    @AreaSelector
    @PostMapping("/v1/add")
    @Operation(summary = "添加道路交限", tags = "2.0")
    public Rule add(@RequestBody RuleAttributeReq req) {
        return ruleService.add(req);
    }

    @AreaSelector
    @PostMapping("/v1/update")
    @Operation(summary = "更新道路交限", tags = "2.0")
    public Rule update(@RequestBody RuleAttributeReq req) {
        return ruleService.update(req);
    }

    @AreaSelector
    @PostMapping("/v1/delete")
    @Operation(summary = "删除道路交限", tags = "2.0")
    public Boolean update(@RequestBody RuleDeleteReq req) {
        return ruleService.update(req);
    }

}
