package work.llm.map.api.matlink;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import work.llm.map.core.aop.AreaSelector;
import work.llm.map.dao.matlink.entity.MatLink;
import work.llm.map.service.matlink.MatLinkService;
import work.llm.map.service.matlink.model.req.MatLinkStatusReq;
import work.llm.map.service.matlink.model.res.MatLinkAllRes;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/mat/link")
@Tag(name = "Mat Link API", description = "道路资料接口")
public class MatLinkController {

    @Resource
    private MatLinkService matLinkService;

    @AreaSelector
    @Operation(summary = "道路资料模板", tags = "1.0")
    @GetMapping("/v1/template")
    public MatLink template(@RequestParam String datasource) {
        return this.matLinkService.template(datasource);
    }

    @AreaSelector
    @Operation(summary = "根据[taskId]和[extent]查询资料", tags = "1.0")
    @GetMapping("/v1/query/extent")
    public List<MatLink> queryBy(@RequestParam(required = false) String taskId,
                                 @RequestParam String extent) {
        return this.matLinkService.queryByExtent(taskId, extent);
    }

    @AreaSelector
    @Operation(summary = "根据ID查询资料", tags = "1.0")
    @GetMapping("/v1/query/id")
    public MatLink queryById(@RequestParam Long id) {
        return this.matLinkService.getById(id);
    }

    @AreaSelector
    @Operation(summary = "根据[taskId]查询未作业资料", tags = "1.0")
    @GetMapping("/v1/query/undo")
    public MatLink queryUndo(@RequestParam String taskId) {
        return this.matLinkService.queryUndo(taskId);
    }

    @AreaSelector
    @Operation(summary = "资料状态更新接口", tags = "1.0")
    @PostMapping("/v1/status")
    public Boolean update(@RequestBody MatLinkStatusReq req) {
        return this.matLinkService.batchStatus(req);
    }

    @AreaSelector
    @Operation(summary = "查找全部资料接口", tags = "2.2")
    @GetMapping("/v1/find/all")
    public List<MatLinkAllRes> all(@RequestParam String taskId) {
        return this.matLinkService.all(taskId);
    }

    @AreaSelector
    @Operation(summary = "查找未作业资料索引", tags = "2.2")
    @Parameters({@Parameter(name = "taskId", description = "任务ID", required = true),
            @Parameter(name = "currentMatId", description = "当前资料ID", required = true),
            @Parameter(name = "findType", description = "查找类型 0-上一条 1-下一条", required = true)})
    @GetMapping("/v1/find/idx")
    public Integer undoIdx(@RequestParam String taskId,
                           @RequestParam Long currentMatId,
                           @RequestParam Integer findType) {
        return this.matLinkService.findIdx(taskId, currentMatId, findType);
    }
}
