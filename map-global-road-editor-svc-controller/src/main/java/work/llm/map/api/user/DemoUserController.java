package work.llm.map.api.user;

import work.llm.map.api.user.model.UserDTO;
import work.llm.map.api.user.model.UserInfoDTO;
import work.llm.map.common.utils.MapperUtils;
import work.llm.map.service.user.UserInfoService;
import work.llm.map.service.user.UserService;
import work.llm.map.service.user.model.UserDO;
import work.llm.map.service.user.model.UserInfoDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.prometheus.client.Counter;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/user")
public class DemoUserController {

	public static final Counter REQUEST_COUNTER = Counter.build()
			.name("http_requests_total")
			.help("Total number of http requests by response status code")
			.labelNames("method")
			.register();

	@Resource
	private UserService userService;
	@Resource
	private UserInfoService userInfoService;

	@PostMapping("/create")
	public void create(@Valid @RequestBody UserDTO param) {
		REQUEST_COUNTER.labels("demo.user.add").inc();
		UserDO userDo = MapperUtils.map(param, UserDO.class);
		userService.createUser(userDo);
	}


	@GetMapping("/get")
	public UserDTO get(Long id) {
		REQUEST_COUNTER.labels("demo.user.get").inc();
		UserDO userDO = userService.getById(id);
		return MapperUtils.map(userDO, UserDTO.class);
	}

	@GetMapping("/getInfo")
	public UserInfoDTO getInfo(Long id) {
		REQUEST_COUNTER.labels("demo.user.info.get").inc();
		UserInfoDO userInfoDo = userInfoService.getById(id);
		return MapperUtils.map(userInfoDo, UserInfoDTO.class);
	}

	@GetMapping("/getPage")
	public Page<UserInfoDTO> getPageInfo(int page, int pageSize) {
		Page<UserInfoDTO> userInfoPage = new Page<>();
		userInfoPage.setCurrent(page);
		userInfoPage.setSize(pageSize);
		userInfoPage.setTotal(userInfoService.getPageCount());
		if (userInfoPage.getTotal() > 0) {
			List<UserInfoDO> list = userInfoService.getPageList(page, pageSize);
			userInfoPage.setRecords(list.stream().map(userInfoPo -> MapperUtils.map(userInfoPo, UserInfoDTO.class)).collect(Collectors.toList()));
		}
		userInfoPage.setPages(userInfoPage.getTotal() / userInfoPage.getSize() + 1);
		return userInfoPage;
	}
}