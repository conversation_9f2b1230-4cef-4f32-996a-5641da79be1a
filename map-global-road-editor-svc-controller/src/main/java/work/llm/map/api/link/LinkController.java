package work.llm.map.api.link;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import work.llm.map.common.constant.Const;
import work.llm.map.core.aop.AreaSelector;
import work.llm.map.dao.link.entity.Link;
import work.llm.map.dao.link.entity.LinkDiff;
import work.llm.map.service.link.LinkDiffService;
import work.llm.map.service.link.LinkService;
import work.llm.map.service.link.model.req.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/link")
@Tag(name = "Link API", description = "道路线接口")
public class LinkController {

    @Resource
    private LinkService linkService;
    @Resource
    private LinkDiffService linkDiffService;

    @Operation(summary = "根据hllLinkid查询Link")
    @GetMapping("/v1/query/id")
    @AreaSelector
    public Link queryByHllLinkid(@RequestParam("hllLinkid") String hllLinkid,
                                 @RequestParam(required = false, defaultValue = Const.DEFAULT_QUERY_STATUS) String status) {
        return linkService.queryById(hllLinkid, status);
    }

    @Operation(summary = "根据范围查询Link")
    @AreaSelector
    @GetMapping("/v1/query/extent")
    public List<Link> queryByExtent(@RequestParam(value = "extent") String extent,
                                    @RequestParam(required = false, defaultValue = Const.DEFAULT_QUERY_STATUS) String status) {
        return linkService.queryByExtent(extent, status);
    }

    @Operation(summary = "拓扑分离")
    @PostMapping("/v1/topology/separate")
    @AreaSelector
    public Boolean topologySeparate(@RequestBody LinkTopologySeparateReq req) {
        return this.linkService.topologySeparate(req);
    }

    @Operation(summary = "拓扑联动")
    @AreaSelector
    @PostMapping("/v1/topology/move")
    public Boolean topologyMove(@RequestBody LinkTopologyMoveReq req) {
        return this.linkService.topologyMove(req);
    }

    @Operation(summary = "修改属性")
    @AreaSelector
    @PostMapping("/v1/attribute/update")
    public Link updateAttribute(@RequestBody LinkAttributeUpdateReq req) {
        return linkService.updateAttribute(req);
    }

    @Operation(summary = "批量删除")
    @PostMapping("/v1/delete")
    @AreaSelector
    public Boolean batchDeleteLink(@RequestBody LinkDeleteBatchReq req) {
        return this.linkService.batchDelete(req);
    }

    @Operation(summary = "资料挂点")
    @PostMapping("/v1/mcn")
    @AreaSelector
    public Boolean connectNode(@RequestBody LinkMaterialConnectReq req) {
        return this.linkService.connectNode(req);
    }


    @Operation(summary = "资料挂线")
    @PostMapping("/v1/mcl")
    @AreaSelector
    public Boolean connectLink(@RequestBody LinkMaterialConnectReq req) {
        return this.linkService.connectLink(req);
    }


    @Operation(summary = "道路挂点")
    @AreaSelector
    @PostMapping("/v1/lcn")
    public Boolean connectLink(@RequestBody LinkConnectReq req) {
        return this.linkService.connectNode(req);
    }

    @Operation(summary = "道路挂线")
    @PostMapping("/v1/lcl")
    @AreaSelector
    public Boolean splitConnectLink(@RequestBody LinkConnectReq req) {
        return this.linkService.connectLink(req);
    }

    @Operation(summary = "指定属性批量修改", tags = "1.0")
    @AreaSelector
    @PostMapping("/v1/attribute/update/batch")
    public List<Link> updateBatchAttribute(@RequestBody LinkAttributeUpdateBatchReq req) {
        return linkService.updateBatchAttribute(req);
    }

    @Operation(summary = "修改形状", tags = "1.0")
    @AreaSelector
    @PostMapping("/v1/shape/update")
    public Boolean updateShape(@RequestBody LinkShapeUpdateReq req) {
        return linkService.updateShape(req);
    }

    @Operation(summary = "根据范围查询Link差分数据", tags = "2.3")
    @AreaSelector
    @GetMapping("/diff/v1/query/extent")
    public List<LinkDiff> queryDiffByExtent(@RequestParam(value = "extent") String extent) {
        return linkDiffService.queryByExtent(extent);
    }
}