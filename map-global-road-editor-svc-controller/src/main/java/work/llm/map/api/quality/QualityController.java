package work.llm.map.api.quality;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import work.llm.map.core.aop.AreaSelector;
import work.llm.map.dao.quality.entity.Quality;
import work.llm.map.service.quality.QualityService;
import work.llm.map.service.quality.model.req.QualityPageReq;
import work.llm.map.service.quality.model.req.QualityUpdateReq;
import work.llm.map.service.quality.model.res.QualityPageRes;

import javax.annotation.Resource;

@Tag(name = "质检API", description = "质检相关接口API")
@RestController
@RequestMapping("/quality")
public class QualityController {

    @Resource
    private QualityService qualityService;

    @AreaSelector
    @GetMapping("/v1/query/page")
    @Operation(summary = "质检列表", tags = "2.2")
    public QualityPageRes queryPage(QualityPageReq req) {
        return qualityService.queryPage(req);
    }

    @AreaSelector
    @GetMapping("/v1/query/mat")
    @Operation(summary = "根据[matId]查询[taskId]绑定的质检数据", tags = "1.0")
    public Quality queryByMat(@RequestParam String taskId, @RequestParam Long matId) {
        return qualityService.queryByMat(taskId, matId);
    }

    @AreaSelector
    @PostMapping("/v1/update")
    @Operation(summary = "质检数据更新接口", tags = "1.0")
    public String update(@RequestBody QualityUpdateReq req) {
        return qualityService.update(req);
    }

}
