package work.llm.map.api.task;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import work.llm.map.core.aop.AreaSelector;
import work.llm.map.service.task.TaskService;
import work.llm.map.service.task.model.req.TaskCreateReq;
import work.llm.map.service.task.model.req.TaskPrecommitReq;
import work.llm.map.service.task.model.res.TaskPrecommitRes;
import work.llm.map.service.task.model.res.TaskWorkInfoRes;

import javax.annotation.Resource;

@Tag(name = "任务API", description = "任务相关接口API")
@RestController
@RequestMapping("/task")
public class TaskController {

    @Resource
    private TaskService taskService;

    @AreaSelector
    @PostMapping("/v1/precommit")
    @Operation(summary = "任务预提交接口", tags = {"1.0", "2.3"})
    public TaskPrecommitRes queryPage(@RequestBody TaskPrecommitReq req) {
        return taskService.precommit(req);
    }

    @AreaSelector
    @PostMapping("/v1/create")
    @Operation(summary = "任务创建接口", tags = "2.0")
    public Boolean create(@RequestBody TaskCreateReq req) {
        return taskService.create(req);
    }

    @AreaSelector
    @GetMapping("/v1/work")
    @Operation(summary = "任务作业信息", tags = {"2.0", "2.3"})
    public TaskWorkInfoRes work(@RequestParam String taskId) {
        return taskService.workInfo(taskId);
    }
}
