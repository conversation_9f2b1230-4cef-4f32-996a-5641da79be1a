package work.llm.map.api.area;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import work.llm.map.core.aop.DefaultSelector;
import work.llm.map.dao.area.entity.Area;
import work.llm.map.service.area.AreaService;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "地区API", description = "地区相关接口API")
@RestController
@RequestMapping("/area")
public class AreaController {

    @Resource
    private AreaService areaService;

    @DefaultSelector
    @GetMapping("/v1/list")
    @Operation(summary = "地区列表")
    public List<Area> list() {
        return areaService.list();
    }
}
