package work.llm.map.api.node;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import work.llm.map.common.constant.Const;
import work.llm.map.core.aop.AreaSelector;
import work.llm.map.dao.node.entity.Node;
import work.llm.map.service.node.NodeService;
import work.llm.map.service.node.model.req.CrossingOperateReq;
import work.llm.map.service.node.model.req.NodeAttributeUpdateReq;
import work.llm.map.service.node.model.res.CrossingOperateRes;
import work.llm.map.service.node.model.res.CrossingRenderRes;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/node")
@Tag(name = "Node API", description = "道路点接口")
public class NodeController {

    @Resource
    private NodeService nodeService;

    @Operation(summary = "根据id查询node")
    @GetMapping("/v1/query/id")
    @AreaSelector
    public Node queryByHllNodeid(@RequestParam("hllNodeid") String hllNodeid,
                                 @RequestParam(required = false, defaultValue = Const.DEFAULT_QUERY_STATUS) String status) {
        return nodeService.queryById(hllNodeid, status);
    }

    @Operation(summary = "根据范围查询Node")
    @AreaSelector
    @GetMapping("/v1/query/extent")
    public List<Node> queryByExtent(@RequestParam(value = "extent") String extent,
                                    @RequestParam(required = false, defaultValue = Const.DEFAULT_QUERY_STATUS) String status) {
        return nodeService.queryByExtent(extent, status);
    }


    @Operation(summary = "修改属性", tags = "1.0")
    @AreaSelector
    @PostMapping("/v1/attribute/update")
    public Node updateAttribute(@RequestBody NodeAttributeUpdateReq req) {
        return nodeService.updateAttribute(req);
    }

    @Operation(summary = "渲染路口", tags = "2.0")
    @AreaSelector
    @GetMapping("/v1/crossing/render")
    public List<CrossingRenderRes> renderCrossing(@RequestParam(value = "extent") String extent) {
        return nodeService.renderCrossing(extent);
    }

    @Operation(summary = "创建路口", tags = "2.2")
    @AreaSelector
    @PostMapping("/v1/crossing/create")
    public CrossingOperateRes createCrossing(@RequestBody CrossingOperateReq req) {
        return nodeService.createCrossing(req);
    }

    @Operation(summary = "更新路口", tags = "2.2")
    @AreaSelector
    @PostMapping("/v1/crossing/update")
    public CrossingOperateRes updateCrossing(@RequestBody CrossingOperateReq req) {
        return nodeService.updateCrossing(req);
    }

    @Operation(summary = "销毁路口", tags = "2.2")
    @AreaSelector
    @PostMapping("/v1/crossing/destroy")
    public CrossingOperateRes destroyCrossing(@RequestBody CrossingOperateReq req) {
        return nodeService.destroyCrossing(req);
    }
}
