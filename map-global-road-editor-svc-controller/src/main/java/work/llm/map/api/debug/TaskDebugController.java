package work.llm.map.api.debug;

import org.springframework.web.bind.annotation.*;
import work.llm.map.facade.model.TaskCreateDTO;
import work.llm.map.facade.service.RoadEditor4TaskFacade;

import javax.annotation.Resource;

/**
 * 调试用HTTP接口，包装Hermes RPC调用
 */
@RestController
@RequestMapping("/debug/task")
public class TaskDebugController {

    @Resource
    private RoadEditor4TaskFacade roadEditor4TaskFacade;

    @PostMapping("/create")
    public boolean createTask(@RequestBody TaskCreateDTO taskCreateDTO) {
        return roadEditor4TaskFacade.createTask(taskCreateDTO);
    }
}