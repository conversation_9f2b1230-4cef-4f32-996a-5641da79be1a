package work.llm.map.api.datamining;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import work.llm.map.core.aop.AreaSelector;
import work.llm.map.dao.datamining.entity.DataminingGroup;
import work.llm.map.service.datamining.DataminingService;
import work.llm.map.service.datamining.model.req.DataminingStatusReq;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/datamining")
@Tag(name = "DATAMINING API", description = "属性资料接口")
public class DataminingController {

    @Resource
    private DataminingService dataminingService;

    @AreaSelector
    @Operation(summary = "根据[taskId]和[extent]查询资料", tags = "2.3")
    @GetMapping("/v1/query/extent")
    public List<DataminingGroup> queryBy(@RequestParam(required = false) String taskId,
                                         @RequestParam String extent) {
        return this.dataminingService.queryByExtent(taskId, extent);
    }

    @AreaSelector
    @Operation(summary = "根据ID查询资料", tags = "2.3")
    @GetMapping("/v1/query/id")
    public DataminingGroup queryById(@RequestParam Long id) {
        return this.dataminingService.queryById(id);
    }

    @AreaSelector
    @Operation(summary = "资料状态更新接口", tags = "2.3")
    @PostMapping("/v1/update")
    public Boolean update(@RequestBody DataminingStatusReq req) {
        return this.dataminingService.batchStatus(req);
    }

    @AreaSelector
    @Operation(summary = "查找未作业的资料（没有时返回第一个资料）", tags = "2.3")
    @GetMapping("/v1/query/one")
    public DataminingGroup queryOne(@RequestParam String taskId) {
        return this.dataminingService.queryOne(taskId);
    }

    @AreaSelector
    @Operation(summary = "通过当前ID查找上一条/下一条资料", tags = "2.3")
    @Parameters({@Parameter(name = "taskId", description = "任务ID", required = true),
            @Parameter(name = "groupId", description = "当前groupId", required = true),
            @Parameter(name = "findType", description = "查找类型 0-上一条 1-下一条", required = true)})
    @GetMapping("/v1/query/current")
    public DataminingGroup queryCurrent(@RequestParam String taskId,
                                        @RequestParam Long groupId,
                                        @RequestParam Integer findType) {
        return this.dataminingService.queryCurrent(taskId, groupId, findType);
    }
}
