<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.lalaframework.boot</groupId>
        <artifactId>lala-boot-parent</artifactId>
        <version>2.10.5.RELEASE</version>
    </parent>

    <groupId>work.llm.map</groupId>
    <artifactId>map-global-road-editor-svc</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>

    <properties>
        <jacoco.version>0.8.4</jacoco.version>
        <surefire.version>2.19.1</surefire.version>
        <modelmapper.version>2.4.4</modelmapper.version>
    </properties>

    <distributionManagement>
        <repository>
            <id>map-releases</id>
            <url>https://maven.huolala.work/repository/map-releases/</url>
        </repository>
        <snapshotRepository>
            <id>map-snapshots</id>
            <url>https://maven.huolala.work/repository/map-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-bom</artifactId>
                <version>${hermes.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.modelmapper</groupId>
                <artifactId>modelmapper</artifactId>
                <version>${modelmapper.version}</version>
            </dependency>
            <dependency>
                <groupId>work.llm.map</groupId>
                <artifactId>map-global-road-editor-svc-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>work.llm.map</groupId>
                <artifactId>map-global-road-editor-svc-dao</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>work.llm.map</groupId>
                <artifactId>map-global-road-editor-svc-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>work.llm.map</groupId>
                <artifactId>map-global-road-editor-svc-facade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>work.llm.map</groupId>
                <artifactId>map-global-road-editor-svc-integration</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>work.llm.map</groupId>
                <artifactId>map-global-road-editor-svc-provider</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>work.llm.map</groupId>
                <artifactId>map-global-road-editor-svc-start</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <modules>
        <module>map-global-road-editor-svc-common</module>
        <module>map-global-road-editor-svc-dao</module>
        <module>map-global-road-editor-svc-facade</module>
        <module>map-global-road-editor-svc-integration</module>
        <module>map-global-road-editor-svc-service</module>
        <module>map-global-road-editor-svc-controller</module>
        <module>map-global-road-editor-svc-provider</module>
        <module>map-global-road-editor-svc-start</module>
        <module>map-global-road-editor-svc-test</module>
    </modules>
</project>
