**模块说明：**
```
<modules>
   <module>xxx-common</module>
   <module>xxx-facade</module>
   <module>xxx-dao</module>
   <module>xxx-provider</module>
   <module>xxx-service</module>
   <module>xxx-integration</module>
   <module>xxx-start</module>
   <module>xxx-test</module>
</modules>
```

 + common 模块公共部分，主要是枚举类和通用的DTO等，以及一些通用的工具类和组件类等。
 + facade 包含所有对外提供服务的接口定义,主要在RPC或SOA模式下使用。
 + dao 数据库，MongoDB，ES，等数据层访问模块。
 + provider 服务的提供方，对外提供服务的实现，此模块只提供标准的SOA(目前是使用JSON-RPC协议)服务实现。
 + service 业务实现层 ,服务的具体的实现。
 + integration 和三方服务集成的模块，依赖其他业务的接口，包含泛化调用以及百度地图调用等三方服务调用。
 + start 启动模块。
 + test 集成测试层，用于单元测试，也提供了JAF各个组件的DEMO使用测试用例。

**facade包的内容说明：**  
```
交互的Java接口interface  
数据传输的dto(request，response，model，bean等)  
跨系统异常的exception  
跨系统依赖的enum，contant等类  
soa的相关依赖：(hermes-api)
dto和exception里面依赖的util  
```

**facade包的Jar包依赖说明：**  
soa的相关依赖：(hermes-api)/(hermes-api-grpc)（千万别直接引用lala-boot-starter-soa）
```
<dependency>  
  <groupId>cn.huolala.arch.hermes</groupId>
  <artifactId>hermes-api</artifactId>
  <version>${jaf.version}</version>  
  <scope>provided</scope>  
</dependency>
```

如有组件方面的使用疑问请参考 https://huolala.feishu.cn/wiki/wikcnIiALjfjfdeEHLh3Lr1iqLb
或飞书 徐键(xylon.xu) 韩瑞鹏(harry.han) 曹伟(james.cao)