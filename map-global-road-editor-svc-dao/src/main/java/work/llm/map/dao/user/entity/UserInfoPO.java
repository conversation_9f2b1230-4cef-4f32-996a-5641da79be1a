package work.llm.map.dao.user.entity;

import cn.lalaframework.orm.mybatis.entity.PO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/12/1
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ci_demo_user_info")
public class UserInfoPO extends PO {
	/**
	 * name
	 */
	private String userName;
	/**
	 * 密码
	 */
	private String userPwd;
	/**
	 * 电话号码
	 */
	private String telNo;
	/**
	 * 身份证
	 */
	private String idNo;
	/**
	 * 密码
	 */
	private String userPwdEncrypt;
	/**
	 * name
	 */
	private String userNameEncrypt;
	/**
	 * 身份证
	 */
	private String idNoEncrypt;
	/**
	 * age
	 */
	private Integer age;
}
