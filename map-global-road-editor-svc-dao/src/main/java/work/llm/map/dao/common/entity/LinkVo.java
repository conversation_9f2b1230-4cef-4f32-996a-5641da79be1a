package work.llm.map.dao.common.entity;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.vividsolutions.jts.geom.Geometry;
import lombok.Data;
import lombok.NoArgsConstructor;
import work.llm.map.common.jackson.deserializer.StringToGeometryDeserializer;
import work.llm.map.common.jackson.serializer.GeometryToStringSerializer;
import work.llm.map.dao.link.entity.Link;

@Data
@NoArgsConstructor
public class LinkVo {

    private String hllLinkid;
    private String hllSNid;
    private String hllENid;
    @JsonSerialize(using = GeometryToStringSerializer.class)
    @JsonDeserialize(using = StringToGeometryDeserializer.class)
    private Geometry geom;
    private Integer status;

    public LinkVo(Link link) {
        this.hllLinkid = link.getHllLinkid();
        this.hllSNid = link.getHllSNid();
        this.hllENid = link.getHllENid();
        this.geom = link.getGeometry();
        this.status = link.getStatus();
    }
}
