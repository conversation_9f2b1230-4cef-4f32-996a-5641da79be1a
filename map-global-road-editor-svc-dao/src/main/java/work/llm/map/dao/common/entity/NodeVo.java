package work.llm.map.dao.common.entity;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.vividsolutions.jts.geom.Geometry;
import lombok.Data;
import lombok.NoArgsConstructor;
import work.llm.map.common.jackson.deserializer.StringToGeometryDeserializer;
import work.llm.map.common.jackson.serializer.GeometryToStringSerializer;
import work.llm.map.dao.node.entity.Node;

@Data
@NoArgsConstructor
public class NodeVo {

    private String hllNodeid;
    @JsonSerialize(using = GeometryToStringSerializer.class)
    @JsonDeserialize(using = StringToGeometryDeserializer.class)
    private Geometry geom;
    private Integer status;

    public NodeVo(Node node) {
        this.hllNodeid = node.getHllNodeid();
        this.geom = node.getGeometry();
        this.status = node.getStatus();
    }
}
