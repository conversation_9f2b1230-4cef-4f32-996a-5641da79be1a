package work.llm.map.dao.matlink.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import work.llm.map.dao.common.entity.TaskCreator;
import work.llm.map.dao.matlink.entity.MatLink;

import java.util.List;

public interface MatLinkMapper extends BaseMapper<MatLink> {

    @Select("SELECT ds_id, collect_version, type, sub_type, task_datasource AS datasource,"
            + " market_code, market_name, COUNT(*) AS count, (SUM(ST_Length(geometry, TRUE))) :: DECIMAL(10, 3) AS mileage"
            + " FROM mat_link WHERE task_created = 0 AND ds_id is not null"
            + " GROUP BY ds_id, collect_version, type, sub_type, task_datasource, market_code, market_name")
    List<TaskCreator> taskCreatable();
}
