package work.llm.map.dao.zlevel.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.vividsolutions.jts.geom.Geometry;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import work.llm.map.common.jackson.deserializer.StringToGeometryDeserializer;
import work.llm.map.common.jackson.deserializer.StringToLocalDateTimeDeserializer;
import work.llm.map.common.jackson.serializer.GeometryToStringSerializer;
import work.llm.map.common.jackson.serializer.LocalDateTimeToStringSerializer;
import work.llm.map.dao.common.GeometryTypeHandler;
import work.llm.map.dao.common.entity.LinkBase;

import java.time.LocalDateTime;

@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@TableName("zlevel_e")
public class Zlevel extends LinkBase {
    @TableId(value = "zlevel_id")
    private String zlevelId;
    private String hllLinkid;
    private Integer pointNum;
    private String hllNodeid;
    private String z;
    @TableField(typeHandler = GeometryTypeHandler.class)
    @JsonSerialize(using = GeometryToStringSerializer.class)
    @JsonDeserialize(using = StringToGeometryDeserializer.class)
    @JsonProperty("geom")
    private Geometry geometry;
    private String linktype;
    private String intrsect;
    private Integer dotShape;
    private String aligned;
    private String levelId;
    private String datasource;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime upDate;
    private Integer status;
    private String taskId;

    @Version
    private Long olv = 0L;
    private String tileId;
    private Integer tileType;


}
