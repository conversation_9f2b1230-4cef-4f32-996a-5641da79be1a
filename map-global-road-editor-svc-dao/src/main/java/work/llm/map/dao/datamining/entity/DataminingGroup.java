package work.llm.map.dao.datamining.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import work.llm.map.common.jackson.deserializer.StringToLocalDateTimeDeserializer;
import work.llm.map.common.jackson.serializer.LocalDateTimeToStringSerializer;

import java.time.LocalDateTime;
import java.util.List;

@Data
@ToString
@EqualsAndHashCode(callSuper = false)
public class DataminingGroup extends Model<DataminingGroup> {

    @TableId(type = IdType.INPUT)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long groupId;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime gpsTime;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime createTime;
    private Integer geomType;
    private Integer type;
    private String content;
    private Integer subType;
    private String description;
    private Integer editFlag;
    private Integer checkStatus;
    private String invalidStatus;
    private String invalidStatusText;
    private String tileId;
    private String marketCode;
    private String marketName;
    private String collectVersion;
    private String datasource;
    private Long dsId;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime updateTime;

    private Integer autoEditFlag;
    private Integer autoFlag;
    private Integer dataWayFlag;

    private String taskDatasource;
    private Integer taskCreated;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime taskCreateTime;
    private String taskId;

    @TableField(exist = false)
    private List<DataminingGeometry> dgs;
    @TableField(exist = false)
    private String currentPosition = "middle";
}
