package work.llm.map.dao.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.vividsolutions.jts.geom.Geometry;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import work.llm.map.common.jackson.deserializer.StringToGeometryDeserializer;
import work.llm.map.common.jackson.deserializer.StringToLocalDateTimeDeserializer;
import work.llm.map.common.jackson.serializer.GeometryToStringSerializer;
import work.llm.map.common.jackson.serializer.LocalDateTimeToStringSerializer;
import work.llm.map.dao.common.GeometryTypeHandler;

import java.time.LocalDateTime;

@Data
@ToString
@EqualsAndHashCode(callSuper = false)
public class Quality extends Model<Quality> {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long qualityId;
    /*资料ID*/
    @JsonSerialize(using = ToStringSerializer.class)
    private Long matId;
    /*坐标*/
    @TableField(typeHandler = GeometryTypeHandler.class)
    @JsonSerialize(using = GeometryToStringSerializer.class)
    @JsonDeserialize(using = StringToGeometryDeserializer.class)
    private Geometry geom;
    /*任务ID*/
    private String taskId;
    /*修改备注*/
    private String memo;
    /*作业员修改状态 1-未修改 2-已修改 3-确认不修改 4-无*/
    private Integer modifyStatus;
    /*质检员修改质检标 1-多 2-错 3-漏 4-正 5-无*/
    private Integer tag;
    /*1-属性 2-路网*/
    private Integer checkAttribute;
    /*质检标错误描述*/
    private String description;
    /*质检员确认状态 1-确认正确 2-确认错误*/
    private Integer confirmStatus;
    /*质检状态 1-未质检 2-已质检 3-无*/
    private Integer qualityStatus;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime createTime;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime updateTime;
}