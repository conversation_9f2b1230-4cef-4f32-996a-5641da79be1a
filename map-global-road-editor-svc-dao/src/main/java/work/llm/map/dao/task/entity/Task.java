package work.llm.map.dao.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import work.llm.map.common.jackson.deserializer.StringToLocalDateTimeDeserializer;
import work.llm.map.common.jackson.serializer.LocalDateTimeToStringSerializer;

import java.time.LocalDateTime;

@Data
@ToString
@EqualsAndHashCode(callSuper = false)
public class Task extends Model<Task> {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    private String taskId;
    private String collectVersion;
    private String tileId;
    private Integer type;
    private Integer subType;
    private String datasource;
    private Long dsId;
    private String marketCode;
    private String marketName;
    private Integer qualityStatus;
    private Integer inspectStatus;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime createTime;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime updateTime;
}
