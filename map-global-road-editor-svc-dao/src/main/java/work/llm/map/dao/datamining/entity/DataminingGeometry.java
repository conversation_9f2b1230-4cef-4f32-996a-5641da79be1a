package work.llm.map.dao.datamining.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.vividsolutions.jts.geom.Geometry;
import lombok.Data;
import lombok.ToString;
import work.llm.map.common.jackson.deserializer.StringToGeometryDeserializer;
import work.llm.map.common.jackson.serializer.GeometryToStringSerializer;
import work.llm.map.dao.common.GeometryTypeHandler;

@Data
@ToString
public class DataminingGeometry {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long groupId;
    private Integer seqNum;
    @TableField(typeHandler = GeometryTypeHandler.class)
    @JsonSerialize(using = GeometryToStringSerializer.class)
    @JsonDeserialize(using = StringToGeometryDeserializer.class)
    @JsonProperty("geom")
    private Geometry geometry;
    private String hllLinkid;
    private String url;
    private String tileId;
}