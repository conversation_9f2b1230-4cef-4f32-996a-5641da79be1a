package work.llm.map.dao.user.entity;

import cn.lalaframework.orm.mybatis.entity.PurePO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/11/30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ci_demo_user")
public class UserPO extends PurePO {

    private static final long serialVersionUID = 1L;
    /**
     * 姓名
     */
    private String name;
    /**
     * 年龄
     */
    private Integer age;
}