package work.llm.map.dao.ds.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@TableName("sys_ds_editor")
@EqualsAndHashCode(callSuper = false)
public class DS extends Model<DS> {

    @TableId
    private Integer id;
    private String name;
    private String ip;
    private String port;
    private String dbname;
    private String username;
    private String password;
    private Integer status;
    private String env;
}
