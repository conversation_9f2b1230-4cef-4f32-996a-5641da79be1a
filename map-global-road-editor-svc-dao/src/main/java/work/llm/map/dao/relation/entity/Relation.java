package work.llm.map.dao.relation.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import work.llm.map.common.jackson.deserializer.StringToLocalDateTimeDeserializer;
import work.llm.map.common.jackson.serializer.LocalDateTimeToStringSerializer;
import work.llm.map.dao.common.entity.LinkVo;
import work.llm.map.dao.common.entity.LinksNodeBase;
import work.llm.map.dao.common.entity.NodeVo;

import java.time.LocalDateTime;
import java.util.List;

@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@TableName("relation_e")
public class Relation extends LinksNodeBase {

    @TableId(value = "relationid", type = IdType.INPUT)
    private String relationId;
    private String type;
    private String tollType;
    private String passNum;
    private String tollForm;
    private String cardType;
    private String veh;
    private String nameCh;
    private String nameFo;
    private String namePh;
    private String nameCht;
    private String gateType;
    private String gateFee;
    private String tlLocat;
    private String tlFlag;
    private String slopetype;
    private String slopeangle;
    private String memo;
    private String meshId;
    private String cp;
    private String datasource;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime upDate;
    private Integer status;
    @Version
    private Long olv = 0L;
    private String tileId;
    private Integer tileType;
    private String taskId;

    @TableField(exist = false)
    private List<LinkVo> links;
    @TableField(exist = false)
    private NodeVo node;

    // 由于LinksNodeBase中有pass，但relation中没有，所以这里使用自己的pass并标识不是数据库字段
    @JsonIgnore
    @TableField(exist = false)
    private String pass;
    private Integer area;
}
