package work.llm.map.dao.datamining.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import work.llm.map.dao.common.entity.TaskCreator;
import work.llm.map.dao.datamining.entity.DataminingGroup;

import java.util.List;

public interface DataminingGroupMapper extends BaseMapper<DataminingGroup> {

    @Select("SELECT ds_id, collect_version, type, sub_type, task_datasource AS datasource,"
            + " market_code, market_name, COUNT(*) AS count"
            + " FROM datamining_group WHERE task_created = 0 AND ds_id is not null"
            + " GROUP BY ds_id, collect_version, type, sub_type, task_datasource, market_code, market_name")
    List<TaskCreator> taskCreatable();
}
