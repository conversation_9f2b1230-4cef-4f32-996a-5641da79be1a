package work.llm.map.dao.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.List;

@Data
public class LinksNodeBase {

    private String inlinkId;
    private String nodeId;
    private String outlinkId;
    private String pass;

    @TableField(exist = false)
    private List<LinkVo> links;
    @TableField(exist = false)
    private NodeVo node;
}
