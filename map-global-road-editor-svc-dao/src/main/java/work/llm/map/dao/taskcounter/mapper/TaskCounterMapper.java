package work.llm.map.dao.taskcounter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import work.llm.map.dao.taskcounter.entity.TaskCounter;

import java.util.List;

public interface TaskCounterMapper extends BaseMapper<TaskCounter> {

    @Select("SELECT task_id::bigint as id, COUNT(*) as material_count FROM mat_link WHERE task_id is not null GROUP BY task_id")
    List<TaskCounter> countGroupByTaskId();
}
