package work.llm.map.dao.link.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.vividsolutions.jts.geom.Geometry;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import work.llm.map.common.jackson.deserializer.StringToGeometryDeserializer;
import work.llm.map.common.jackson.deserializer.StringToLocalDateTimeDeserializer;
import work.llm.map.common.jackson.serializer.GeometryToStringSerializer;
import work.llm.map.common.jackson.serializer.LocalDateTimeToStringSerializer;
import work.llm.map.dao.common.GeometryTypeHandler;

import java.time.LocalDateTime;

@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@TableName("link_e")
public class Link extends Model<Link> {

    @TableId(value = "hll_linkid", type = IdType.INPUT)
    private String hllLinkid;
    private String linkId;
    private String hllSNid;
    private String hllENid;
    private String kind;
    private String formway;
    private String dir;
    private String app;
    private String toll;
    private String adopt;
    private String md;
    private String devs;
    private String spet;
    private String funct;
    private String urban;
    private String pave;
    private Integer laneN;
    private Integer laneL;
    private Integer laneR;
    private String laneC;
    private String width;
    private String viad;
    private String lAdmin;
    private String rAdmin;
    private String tAdmin;
    private String timeZone;
    @TableField(typeHandler = GeometryTypeHandler.class)
    @JsonSerialize(using = GeometryToStringSerializer.class)
    @JsonDeserialize(using = StringToGeometryDeserializer.class)
    @JsonProperty("geom")
    private Geometry geometry;
    private Double len;
    private String fSpeed;
    private String tSpeed;
    private String spClass;
    @TableField(value = "ar_veh")
    private String arVeh;
    private String diciType;
    private String verifyflag;
    private String preLaunch;
    private String nameChO;
    private String nameChA;
    private String nameChF;
    private String namePhO;
    private String namePhA;
    private String namePhF;
    private String nameEnO;
    private String nameEnA;
    private String nameEnF;
    private String namePo;
    private String nameCht;
    private String codeType;
    private String nameType;
    private String srcFlag;
    private String meshId;
    private String memo;
    private String cp;
    private String datasource;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime upDate;
    private Integer status;
    private String geomwkt;
    private String divider;
    private String dividerLeg;
    private String pubAccess;
    @Version
    private Long olv = 0L;
    private String linkOld;
    private String linkNew;
    private String tileId;
    private Integer tileType;
    private String taskId;
    private Integer area;
    private String name;
    private String nmChoLangcd;
    private String nmChaLangcd;
    private String nmChfLangcd;

    @TableField(exist = false)
    private Integer covered = 0;
}
