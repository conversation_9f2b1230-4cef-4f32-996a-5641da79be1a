package work.llm.map.dao.link.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.vividsolutions.jts.geom.Geometry;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import work.llm.map.common.jackson.deserializer.StringToGeometryDeserializer;
import work.llm.map.common.jackson.deserializer.StringToLocalDateTimeDeserializer;
import work.llm.map.common.jackson.serializer.GeometryToStringSerializer;
import work.llm.map.common.jackson.serializer.LocalDateTimeToStringSerializer;
import work.llm.map.dao.common.GeometryTypeHandler;

import java.time.LocalDateTime;

@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@TableName("link_diff")
public class LinkDiff extends Model<LinkDiff> {

    @TableId(value = "hll_linkid", type = IdType.INPUT)
    private String hllLinkid;
    private String linkId;
    private String dir;
    private String kind;
    private String formway;
    @TableField(typeHandler = GeometryTypeHandler.class)
    @JsonSerialize(using = GeometryToStringSerializer.class)
    @JsonDeserialize(using = StringToGeometryDeserializer.class)
    @JsonProperty("geom")
    private Geometry geometry;
    private String nameChO;
    private String datasource;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime upDate;
    private Integer status;
    private String tileId;
    private String taskId;
}
