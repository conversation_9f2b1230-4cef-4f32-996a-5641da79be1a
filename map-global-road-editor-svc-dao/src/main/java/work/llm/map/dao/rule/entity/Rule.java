package work.llm.map.dao.rule.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import work.llm.map.common.jackson.deserializer.StringToLocalDateTimeDeserializer;
import work.llm.map.common.jackson.serializer.LocalDateTimeToStringSerializer;
import work.llm.map.dao.common.entity.LinkVo;
import work.llm.map.dao.common.entity.LinksNodeBase;
import work.llm.map.dao.common.entity.NodeVo;

import java.time.LocalDateTime;
import java.util.List;

@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@TableName("rule_e")
public class Rule extends LinksNodeBase {

    @TableId(value = "rule_id", type = IdType.INPUT)
    private String ruleId;
    private String pass2;
    private Integer flag;
    private String vperiod;
    private String vehclType;
    private String vpdir;
    private String meshList;
    private String memo;
    private String cp;
    private String datasource;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime upDate;
    private Integer status;
    private String linkAngle;
    @Version
    private Long olv = 0L;
    private String tileId;
    private Integer tileType;
    private String taskId;
    private Integer ruleInfo;
    private Integer area;

    @TableField(exist = false)
    private List<LinkVo> links;
    @TableField(exist = false)
    private NodeVo node;
}
