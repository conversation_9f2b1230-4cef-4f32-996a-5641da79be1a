package work.llm.map.dao.matlink.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.vividsolutions.jts.geom.Geometry;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import work.llm.map.common.jackson.deserializer.StringToGeometryDeserializer;
import work.llm.map.common.jackson.deserializer.StringToLocalDateTimeDeserializer;
import work.llm.map.common.jackson.serializer.GeometryToStringSerializer;
import work.llm.map.common.jackson.serializer.LocalDateTimeToStringSerializer;
import work.llm.map.dao.common.GeometryTypeHandler;

import java.time.LocalDateTime;

@Data
@ToString
@EqualsAndHashCode(callSuper = false)
public class MatLink extends Model<MatLink> {

    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long matId;
    private String hllLinkid;
    private String linkId;
    private String hllSNid;
    private String hllENid;
    private String kind;
    private String formway;
    private String dir;
    private String app;
    private String toll;
    private String adopt;
    private String md;
    private String devs;
    private String spet;
    private String funct;
    private String urban;
    private String pave;
    private Integer laneN;
    private Integer laneL;
    private Integer laneR;
    private String laneC;
    private String width;
    private String viad;
    private String lAdmin;
    private String rAdmin;
    private String tAdmin;
    private String timeZone;
    @TableField(typeHandler = GeometryTypeHandler.class, value = "geometry")
    @JsonSerialize(using = GeometryToStringSerializer.class)
    @JsonDeserialize(using = StringToGeometryDeserializer.class)
    @JsonProperty("geom")
    private Geometry geometry;
    private Double len;
    private String fSpeed;
    private String tSpeed;
    private String spClass;
    @TableField(value = "ar_veh")
    private String arVeh;
    private String diciType;
    private String verifyflag;
    private String preLaunch;
    private String nameChO;
    private String nameChA;
    private String nameChF;
    private String namePhO;
    private String namePhA;
    private String namePhF;
    private String nameEnO;
    private String nameEnA;
    private String nameEnF;
    private String namePo;
    private String nameCht;
    private String codeType;
    private String nameType;
    private String srcFlag;
    private String meshId;
    private String memo;
    private String cp;
    private String datasource;
    private Integer status;
    private String geomwkt;
    private String divider;
    private String dividerLeg;
    private String pubAccess;
    private Integer workStatus;
    private String collectVersion;
    private Long dsId;
    private Integer tileType;
    private String tileId;
    private String realLinkid;
    private String taskId;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime createTime;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime updateTime;
    private Integer type;
    private Integer subType;
    private String marketCode;
    private String marketName;
    private String taskDatasource;
    private Integer taskCreated;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime taskCreateTime;
}
