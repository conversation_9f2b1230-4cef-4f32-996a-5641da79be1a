package work.llm.map.dao.common;

import com.vividsolutions.jts.geom.Geometry;
import com.vividsolutions.jts.io.ParseException;
import com.vividsolutions.jts.io.WKBReader;
import com.vividsolutions.jts.io.WKBWriter;
import com.vividsolutions.jts.io.WKTReader;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedTypes({Geometry.class})
public class GeometryTypeHandler extends BaseTypeHandler<Geometry> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Geometry parameter, JdbcType jdbcType) throws SQLException {
        WKTReader reader = new WKTReader();
        WKBWriter writer = new WKBWriter(2, true);
        try {
            Geometry geometry = reader.read(parameter.toText());
            geometry.setSRID(4326);
            byte[] data = writer.write(geometry);
            ps.setBytes(i, data);
        } catch (ParseException e) {
            throw new SQLException("GeometryTypeHandler set value error");
        }
    }

    @Override
    public Geometry getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getGeometry(rs.getString(columnName));

    }

    @Override
    public Geometry getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getGeometry(rs.getString(columnIndex));
    }

    @Override
    public Geometry getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getGeometry(cs.getString(columnIndex));
    }

    private Geometry getGeometry(String columnValue) throws SQLException {
        if (StringUtils.isBlank(columnValue)) return null;
        WKBReader reader = new WKBReader();
        Geometry geometry;
        try {
            geometry = reader.read(WKBReader.hexToBytes(columnValue));
        } catch (ParseException e) {
            throw new SQLException("GeometryTypeHandler read value error");
        }
        return geometry;
    }
}
