package work.llm.map.dao.user;

import work.llm.map.dao.user.entity.UserInfoPO;
import work.llm.map.dao.user.mapper.UserInfoMapper;
import cn.lalaframework.orm.base.dao.AbstractDao;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/1
 */
@Repository
public class UserInfoDao extends AbstractDao<UserInfoMapper, UserInfoPO> {

    public List<UserInfoPO> queryPageList(int page, int pageSize){
        PageHelper.startPage(page, pageSize);
        return mapper.selectList(Wrappers.emptyWrapper());
    }

    public Integer queryPageCount(){
        //return mapper.selectCount(Wrappers.emptyWrapper());
        return 0;
    }

}
