package work.llm.map.dao.node.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.vividsolutions.jts.geom.Geometry;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import work.llm.map.common.jackson.deserializer.StringToGeometryDeserializer;
import work.llm.map.common.jackson.deserializer.StringToLocalDateTimeDeserializer;
import work.llm.map.common.jackson.serializer.GeometryToStringSerializer;
import work.llm.map.common.jackson.serializer.LocalDateTimeToStringSerializer;
import work.llm.map.dao.common.EmptyToNullMetaHandler;
import work.llm.map.dao.common.GeometryTypeHandler;

import java.time.LocalDateTime;

@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@TableName("node_e")
public class Node extends Model<Node> {

    @TableId(value = "hll_nodeid", type = IdType.INPUT)
    private String hllNodeid;
    @TableField(typeHandler = EmptyToNullMetaHandler.class)
    private String kind;
    @TableField(typeHandler = GeometryTypeHandler.class)
    @JsonSerialize(using = GeometryToStringSerializer.class)
    @JsonDeserialize(using = StringToGeometryDeserializer.class)
    @JsonProperty("geom")
    private Geometry geometry;
    private String nameCh;
    private String nameFo;
    private String nameCht;
    private String namePh;
    private String adjoinMid;
    private String adjoinNid;
    private String type;
    @TableField(typeHandler = EmptyToNullMetaHandler.class)
    private String mainnodeid;
    @TableField(typeHandler = EmptyToNullMetaHandler.class)
    private String subnodeid;
    @TableField(typeHandler = EmptyToNullMetaHandler.class)
    private String subnodeid2;
    private String light;
    private String isPbnode;
    private String cp;
    private String datasource;
    private String nodeId;
    @JsonDeserialize(using = StringToLocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeToStringSerializer.class)
    private LocalDateTime upDate;
    private String memo;
    private Integer status;
    private String geomwkt;
    @Version
    private Long olv = 0L;
    private String tileId;
    private Integer tileType;
    private String taskId;
    private Integer area;
    @TableField(exist = false)
    private Integer connectLinkNum;
}
