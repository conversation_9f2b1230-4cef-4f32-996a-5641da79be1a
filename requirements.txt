# Core dependencies
asyncio-pool>=0.6.0
aiohttp>=3.8.0
asyncpg>=0.28.0
psycopg2-binary>=2.9.0
PyYAML>=6.0

# Database and connection pooling
SQLAlchemy>=1.4.0
psycopg2-pool>=1.1

# Performance and utilities
numpy>=1.21.0
pandas>=1.3.0  # Optional: for data analysis and debugging

# Logging and monitoring
structlog>=22.0.0  # Optional: for structured logging

# Development and testing (optional)
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=22.0.0
flake8>=5.0.0

# Geometry processing (if needed for advanced geometry operations)
Shapely>=1.8.0  # Optional: for geometry validation and processing

# Progress tracking (optional)
tqdm>=4.64.0  # For progress bars during processing
