<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>work.llm.map</groupId>
        <artifactId>map-global-road-editor-svc</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <description>interface definition</description>
    <artifactId>map-global-road-editor-svc-facade</artifactId>
	
	<dependencies>
        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>hermes-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!-- 不要添加其他依赖 facade是接口定义 尽可能减少不必要依赖 不然容易传递脏依赖 -->
	</dependencies>
</project>