package work.llm.map.facade.service;

import work.llm.map.facade.model.UserDTO;
import work.llm.map.facade.model.UserPage;
import cn.huolala.arch.hermes.common.tool.Pair;
import java.util.List;

/**
 * <AUTHOR>
 * 标准JSONRPC服务定义
 */
public interface DemoUserStandardFacade {

    /**
     * 创建用户
     * @param userDTO 用户信息
     */
    void createUser(UserDTO userDTO);

    /**
     * 根据id查找
     * @param id id
     * @return {@link UserDTO}
     */
    UserDTO getById(Long id);

    /**
     * 根据名称查找用户集合
     * @param name 名称
     * @return {@link List <UserPO>}
     */
    List<UserDTO> listByName(String name);

    /**
     * 获取分页信息
     * @param page 页码
     * @param pageSize 每页条数
     * @return
     */
    UserPage getUserPage(int page, int pageSize);
}
