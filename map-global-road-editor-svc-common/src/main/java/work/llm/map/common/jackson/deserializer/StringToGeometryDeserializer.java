package work.llm.map.common.jackson.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdScalarDeserializer;
import com.vividsolutions.jts.geom.Geometry;
import com.vividsolutions.jts.io.ParseException;
import com.vividsolutions.jts.io.WKTReader;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

public class StringToGeometryDeserializer extends StdScalarDeserializer<Geometry> {

    public final static StringToGeometryDeserializer instance = new StringToGeometryDeserializer();

    protected StringToGeometryDeserializer() {
        super(Geometry.class);
    }

    @Override
    public Geometry deserialize(JsonParser p, DeserializationContext context) throws IOException {
        String value = p.getValueAsString();
        if (StringUtils.isBlank(value)) throw new RuntimeException("The 'geom' field cannot be empty.");
        try {
            return new WKTReader().read(value);
        } catch (ParseException e) {
            throw new RuntimeException("Failed to parse the field 'geom'");
        }
    }
}
