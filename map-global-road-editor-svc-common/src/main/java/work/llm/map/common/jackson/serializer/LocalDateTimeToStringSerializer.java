package work.llm.map.common.jackson.serializer;

import com.fasterxml.jackson.databind.ser.std.ToStringSerializerBase;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

public class LocalDateTimeToStringSerializer extends ToStringSerializerBase {

    String DATEFORMAT_NORMAL_DATE = "yyyy-MM-dd HH:mm:ss";
    public final static LocalDateTimeToStringSerializer instance = new LocalDateTimeToStringSerializer();

    public LocalDateTimeToStringSerializer() {
        super(Object.class);
    }

    @Override
    public String valueToString(Object value) {
        LocalDateTime ldt = (LocalDateTime) value;
        if (Objects.isNull(value)) return null;
        return ldt.format(DateTimeFormatter.ofPattern(DATEFORMAT_NORMAL_DATE));
    }
}
