package work.llm.map.common.exception;

import work.llm.map.common.result.Result;
import cn.lalaframework.exception.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @createTime 2020-07-27 4:41 PM
 * @description 业务的错误码
 */
@Getter
@RequiredArgsConstructor
public enum BizErrorCode implements ErrorCode {
    /** 成功 */
    SUCCESS(0, "svc.success", "success"),

    /** 用户侧错误以1开头 */
    BIND_FAIL(10001, "svc.bind.failed", "Request parameter bind failed"),
    VALIDATE_FAIL(10002, "svc.validate.failed", "Request parameter validation failed"),

    /** 系统侧错误以2开头 */
    SYSTEM_ERROR(20001, "svc.error", "Service is not available");

    private final Integer ret;
    private final String code;
    private final String msg;


    public BizException getException() {
        return new BizException(this);
    }

    public BizException getException(String msg) {
        return new BizException(this.getRet(), this.getCode(), msg);
    }

    public BizException getException(Object data) {
        return new BizException(this, data);
    }

    public Result<Object> getResult(){
        return new Result<>(this.getRet(), this.getMsg(), null);
    }

    public Result<Object> getResult(String msg){
        return new Result<>(this.getRet(), msg, null);
    }
}
