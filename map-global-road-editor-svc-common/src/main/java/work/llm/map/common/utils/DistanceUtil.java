package work.llm.map.common.utils;

import com.vividsolutions.jts.geom.Coordinate;
import com.vividsolutions.jts.geom.Geometry;

public class DistanceUtil {

    private final static double EARTH_RADIUS = 6378.137;

    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }

    /**
     * 计算道路长度，单位：米.
     */
    public static double distance(Geometry geometry) {
        Coordinate[] coordinates = geometry.getCoordinates();
        double radLat1;
        double radLat2;
        Coordinate coordinate1;
        Coordinate coordinate2;
        double a;
        double b;
        double length = 0;
        double s;
        for (int i = 0; i < coordinates.length - 1; i++) {
            coordinate1 = coordinates[i];
            coordinate2 = coordinates[i + 1];
            radLat1 = rad(coordinate1.y);
            radLat2 = rad(coordinate2.y);
            a = radLat1 - radLat2;
            b = rad(coordinate1.x) - rad(coordinate2.x);
            s = 2 * Math.asin(Math.sqrt(
                    Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
            s = s * EARTH_RADIUS;
            s = (s * 10000) / 10;
            length += s;
        }
        return Double.parseDouble(String.format("%.2f", length));
    }
}
