package work.llm.map.common.utils;

import org.modelmapper.AbstractConverter;
import org.modelmapper.Converter;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeToken;
import org.modelmapper.convention.MatchingStrategies;

import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * object mapping utils
 *
 * <AUTHOR>
 * @date 2021/7/6
 */
public class MapperUtils {

    private static final Converter<Date, String> DATE_TO_STRING_CONVERTER =
            new AbstractConverter<Date, String>() {
                @Override
                protected String convert(Date date) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                    return date == null ? null : simpleDateFormat.format(date);
                }
            };

    private static final ModelMapper INSTANCE = new ModelMapper();

    static {
        INSTANCE.addConverter(DATE_TO_STRING_CONVERTER);
        INSTANCE.getConfiguration().setDeepCopyEnabled(true).setMatchingStrategy(MatchingStrategies.STRICT).setFullTypeMatchingRequired(true);
    }

    private MapperUtils() {
    }

    public static <D> D map(Object source, Class<D> destinationType) {
        if (source == null) {
            return null;
        }
        return INSTANCE.map(source, destinationType);
    }

    public static <D> D map(Object source, Type destinationType) {
        if (source == null) {
            return null;
        }
        return INSTANCE.map(source, destinationType);
    }

    public static <D> D map(Object source, TypeToken<D> typeToken) {
        return map(source, typeToken.getType());
    }

    public static void map(Object source, Object destination) {
        if (source == null) {
            return;
        }
        INSTANCE.map(source, destination);
    }

    public static <S, D> List<D> mapList(List<S> source, Class<D> destinationType) {
        return map(source, destinationType).collect(Collectors.toList());
    }

    public static <S, D> Stream<D> map(Collection<S> source, Class<D> destinationType) {
        return source.stream().map(s -> map(s, destinationType));
    }
}