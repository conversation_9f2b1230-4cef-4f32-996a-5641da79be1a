package work.llm.map.common.jackson.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class StringToLocalDateTimeDeserializer extends StdDeserializer<LocalDateTime> {

    String DATEFORMAT_NORMAL_DATE = "yyyy-MM-dd HH:mm:ss";
    public final static StringToLocalDateTimeDeserializer instance = new StringToLocalDateTimeDeserializer();

    protected StringToLocalDateTimeDeserializer() {
        super(LocalDateTime.class);
    }

    @Override
    public LocalDateTime deserialize(JsonParser p, DeserializationContext context) throws IOException {
        String value = p.getValueAsString();
        if (StringUtils.isEmpty(value)) return null;
        return LocalDateTime.parse(value, DateTimeFormatter.ofPattern(DATEFORMAT_NORMAL_DATE));
    }
}
