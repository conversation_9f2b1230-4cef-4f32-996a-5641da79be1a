package work.llm.map.common.constant;

@SuppressWarnings("unused")
public interface Const {

    Integer RULE_INFO_UNKNOWN = 0;
    Integer RULE_INFO_FORWARD = 1;
    Integer RULE_INFO_LEFT = 2;
    Integer RULE_INFO_RIGHT = 3;
    Integer RULE_INFO_TURN = 4;
    Integer RULE_INFO_ALLOW_TURN = 5;

    String LINK_DIR_UNKNOWN = "0";
    String LINK_DIR_DOUBLE = "1";
    String LINK_DIR_FORWARD = "2";
    String LINK_DIR_BACKWARD = "3";
    String LINK_DIR_DOUBLE_NO = "4";

    String DEFAULT_QUERY_STATUS = "0,2,3";

    String RELATION_TYPE_TRAFFIC_LIGHTS = "6";

    String SYMBOL_COMMA = ",";
    String SYMBOL_PIPE = "|";
    String SYMBOL_PIPE_REGEX = "\\|";

    Integer YES = 1;
    Integer NO = 0;

    Integer TILE_TYPE_DEFAULT = 1;

    String DEFAULT_NODE_KIND = "10ff";
    String NODE_LIGHT_DEFAULT = "0";

    String LINK_FORMWAY_NONE = "1";
    String LINK_FORMWAY_CROSSING = "50";

    String NODE_TYPE_DEFAULT = "0";
    String NODE_TYPE_CROSSING_SUB_NODE = "1";
    String NODE_TYPE_CROSSING_SINGLE = "2";
    String NODE_TYPE_CROSSING_MAIN_NODE = "3";

    int STATUS_DELETED = 1;
    int STATUS_UPDATED = 2;
    int STATUS_NEW = 3;

    int WORK_STATUS_DRAW = -1;
    int WORK_STATUS_DEFAULT = 0;
    int WORK_STATUS_WORKED = 1;
    int WORK_STATUS_DELETED = 2;

    //"质检状态 1-未质检 2-已质检 3-无"
    int QUALITY_STATUS_UNQUALIFIED = 1;
    int QUALITY_STATUS_QUALIFIED = 2;
    int QUALITY_STATUS_NO = 3;

    // 质检员修改质检标 1-多 2-错 3-漏 4-正 5-无
    int TAG_MULTI = 1;
    int TAG_ERROR = 2;
    int TAG_LOSS = 3;
    int TAG_CORRECT = 4;
    int TAG_NO = 5;

    /*2  路网更新
    6  检查项
    7   用反调查
    8   切版融合*/
    Integer TYPE_ROAD = 2;
    Integer TYPE_CHECK = 6;
    Integer TYPE_FEEDBACK = 7;
    Integer TYPE_FUSION = 8;

    /*
    *   路网更新(2)：
        50001   路网缺失
        50002   路网冗余
        50003   属性更新
        50004   切版缺失
        50005   切版冗余
        50006   切版属性
        50007   切版路形
        检查项(6)：
        60003  全要素
        60007 检查log修改
        用反调查(7)：
        70001 用反调查
    * */
    Integer SUB_TYPE_LINK_MISS = 50001;
    Integer SUB_TYPE_LINK_REDUNDANT = 50002;
    Integer SUB_TYPE_LINK_ATTRIBUTE = 50003;
    Integer SUB_TYPE_VS_MISS = 50004;
    Integer SUB_TYPE_VS_REDUNDANT = 50005;
    Integer SUB_TYPE_VS_ATTRIBUTE = 50006;
    Integer SUB_TYPE_VS_GEOMETRY = 50007;
    Integer SUB_TYPE_CHECK_FULL = 60003;
    Integer SUB_TYPE_CHECK_LOG = 60007;
    Integer SUB_TYPE_FEEDBACK = 70001;

    // 1-开始作业 2-作业提交 3-开始质检 4-质检提交 5-质检返工 6-质检返修 7-开始验收 8-验收提交 9-验收返工
    int COMMIT_TYPE_START_WORK = 1;
    int COMMIT_TYPE_WORK_SUBMIT = 2;
    int COMMIT_TYPE_START_QUALITY = 3;
    int COMMIT_TYPE_QUALITY_SUBMIT = 4;
    int COMMIT_TYPE_QUALITY_REWORK = 5;
    int COMMIT_TYPE_QUALITY_REPAIR = 6;
    int COMMIT_TYPE_START_VERIFY = 7;
    int COMMIT_TYPE_VERIFY_SUBMIT = 8;
    int COMMIT_TYPE_VERIFY_REWORK = 9;

    // 质检状态
    int TASK_QUALITY_STATUS_DEFAULT = 0; // 默认
    int TASK_QUALITY_STATUS_STARTED = 1; // 开始质检
    int TASK_QUALITY_STATUS_SUBMIT = 2; // 质检提交
    int TASK_QUALITY_STATUS_REPAIR = 3; // 质检返修
    int TASK_QUALITY_STATUS_REWORK = 4; // 质检返工
    // 验收状态
    int TASK_INSPECT_STATUS_DEFAULT = 0; // 默认
    int TASK_INSPECT_STATUS_SUBMIT = 1; // 验收提交
    int TASK_INSPECT_STATUS_REWORK = 3; // 验收返工
    //int TASK_INSPECT_STATUS_REPAIR = 2; // 验收返修

    // 任务类型 1-属性 2-路网
    int TASK_TYPE_ATTRIBUTE = 1;
    int TASK_TYPE_ROAD = 2;

    // 质检员确认状态 1-确认正确 2-确认错误 3-无"
    int QUALITY_CONFIRM_STATUS_CORRECT = 1;
    int QUALITY_CONFIRM_STATUS_ERROR = 2;
    int QUALITY_CONFIRM_STATUS_NO = 3;

    //作业员修改状态 1-未修改 2-已修改 3-确认不修改 4-无
    int MODIFY_STATUS_UNMODIFIED = 1;
    int MODIFY_STATUS_MODIFIED = 2;
    int MODIFY_STATUS_CONFIRM_NO = 3;
    int MODIFY_STATUS_NO = 4;

    //返工返修标记: 0:默认正常,不驳回, 1:返修, 2:返工
    int REBUT_TAG_DEFAULT = 0;
    int REBUT_TAG_REPAIR = 1;
    int REBUT_TAG_REWORK = 2;

    String LINK = "LINK";
    String NODE = "NODE";
    String RELATION = "RELATION";
    String RULE = "RULE";
//    String TPLINK = "货车限制";
//    String TPRULE = "货车交限";
//    String DR = "方向信息";
//    String TOLLGATEDR = "收费站信息";
//    String IC = "IC名称";
//    String LNGROUP = "车道信息组";
//    String LN = "车道信息";
//    String POINTSPEED = "点限速";
//    String ZLEVEL = "立交点";
//    String CAMERA = "电子眼";
//    String PAIRCAMERA = "区间电子眼";
//    String TRFCSIGN = "警示信息";
//    String TPRA = "货车限制通行";
//    String BR = "放大图";
//    String DM = "3D路口图";
//    String LSLINE = "长白实线";

    // 属性资料类型 1-点 2-线 3-面
    Integer DATAMINING_GEOM_TYPE_POINT = 1;
    Integer DATAMINING_GEOM_TYPE_LINE = 2;
    Integer DATAMINING_GEOM_TYPE_POLYGON = 3;
}
