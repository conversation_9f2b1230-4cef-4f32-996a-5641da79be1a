package work.llm.map.common.utils;

import com.vividsolutions.jts.geom.Coordinate;
import com.vividsolutions.jts.geom.Geometry;
import com.vividsolutions.jts.io.ParseException;
import com.vividsolutions.jts.io.WKTReader;
import lombok.extern.slf4j.Slf4j;
import work.llm.map.common.exception.BizException;

@Slf4j
public class ExtentUtil {

    public static void exceed(String range) {
        WKTReader wktReader = new WKTReader();
        try {
            Geometry geometry = wktReader.read(range);

            Coordinate[] coordinates = geometry.getCoordinates();
            Coordinate c1 = coordinates[0];
            Coordinate c3 = coordinates[2];
            double x_range = Math.abs(c3.x - c1.x);
            double y_range = Math.abs(c3.y - c1.y);

            if (y_range > 0.025 || x_range > 0.04) {
                throw new BizException("Request range is too large");
            }
        } catch (ParseException e) {
            throw new BizException("The field 'extent' parsing error");
        }
    }
}
