package work.llm.map.common.jackson.serializer;

import com.fasterxml.jackson.databind.ser.std.ToStringSerializerBase;
import com.vividsolutions.jts.geom.Geometry;

import java.util.Objects;

public class GeometryToStringSerializer extends ToStringSerializerBase {

    public final static GeometryToStringSerializer instance = new GeometryToStringSerializer();

    public GeometryToStringSerializer() {
        super(Object.class);
    }

    @Override
    public String valueToString(Object value) {
        Geometry geometry = (Geometry) value;
        if (Objects.isNull(value)) return null;
        return geometry.toText();
    }
}
