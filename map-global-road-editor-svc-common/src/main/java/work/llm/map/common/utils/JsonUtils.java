package work.llm.map.common.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 * @createTime 2020-07-30 10:20 AM
 * @description json的工具类
 */

public class JsonUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(JsonUtils.class);
    private static final JsonMapper MAPPER = new JsonMapper();
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    static {
        MAPPER.setDateFormat(DATE_FORMAT);
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static String toJsonString(Object args) {
        try {
            return MAPPER.writeValueAsString(args);
        } catch (JsonProcessingException e) {
            LOGGER.error("json format occur err,data is :{},ex is:{}", args, e.getMessage(), e);
        }
        return "";
    }

    public static <T> T parseJson(String args, Class<T> clazz) throws JsonProcessingException {
        return MAPPER.readValue(args, clazz);
    }

    public static <T> T parseJson(String args, TypeReference<T> javaType) throws JsonProcessingException {
        return MAPPER.readValue(args, javaType);

    }

    public static <T> T parseJson(InputStream inputStream, TypeReference<T> javaType) throws IOException {
        return MAPPER.readValue(inputStream, javaType);

    }
}
