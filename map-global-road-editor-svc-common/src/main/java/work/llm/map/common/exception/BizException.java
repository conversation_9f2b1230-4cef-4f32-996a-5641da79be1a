package work.llm.map.common.exception;

import cn.lalaframework.exception.ErrorCode;
import cn.lalaframework.exception.ServiceException;

/**
 * 业务异常，可能异常携带数据
 *
 * <AUTHOR>
 * @date 2021/11/30
 */
public class BizException extends ServiceException {

    private static final long serialVersionUID = 1L;

    private final Object data;

    public BizException(String msg) {
        this(BizErrorCode.SYSTEM_ERROR.getRet(), BizErrorCode.SYSTEM_ERROR.getCode(), msg);
    }

    public BizException(int ret, String msg) {
        this(ret, "", msg);
    }

    public BizException(ErrorCode errorCode) {
        this(errorCode.getRet(), errorCode.getCode(), errorCode.getMsg());
    }

    public BizException(int ret, String code, String msg) {
        this(ret, code, msg, null);
    }

    public BizException(ErrorCode errorCode, Object data) {
        this(errorCode.getRet(), errorCode.getCode(), errorCode.getMsg(), data);
    }

    public BizException(int ret, String code, String msg, Object data) {
        super(ret, code, msg);
        this.data = data;
    }

    public Object getData() {
        return data;
    }
}