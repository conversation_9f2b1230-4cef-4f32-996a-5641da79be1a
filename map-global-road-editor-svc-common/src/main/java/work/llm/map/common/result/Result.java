package work.llm.map.common.result;


import cn.lalaframework.exception.ErrorCode;
import cn.lalaframework.exception.LalaException;
import lombok.Data;

import java.io.Serializable;

/**
 * 通用返回结果
 *
 * <AUTHOR>
 * @date 2021/11/30
 */
@Data
public class Result<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    private int ret;
    private String msg;
    private T data;

    public Result() {
    }
    public Result(int ret, String msg) {
        this(ret, msg, null);
    }
    public Result(int ret, String msg, T data) {
        this.data = data;
        this.ret = ret;
        this.msg = msg;
    }

    public static <T> Result<T> success(T data) {
        return new Result<>(0, "success", data);
    }

    public static Result<?> fail(int ret, String msg) {
        return new Result<>(ret, msg);
    }

    public static Result<?> fail(LalaException exception) {
        return new Result<>(exception.getRet(), exception.getMessage());
    }

    public static Result<?> fail(ErrorCode errorCode) {
        return new Result<>(errorCode.getRet(), errorCode.getMsg());
    }
}