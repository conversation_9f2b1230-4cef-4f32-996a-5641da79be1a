package work.llm.map.demo.mysql.service.impl;

import work.llm.map.common.utils.MapperUtils;
import work.llm.map.dao.user.UserInfoDao;
import work.llm.map.dao.user.entity.UserInfoPO;
import work.llm.map.demo.mysql.service.MysqlService;
import work.llm.map.service.user.model.UserInfoDO;
import cn.lalaframework.dynamic.datasource.annotation.DS;
import cn.lalaframework.dynamic.datasource.annotation.DSTransactional;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@DS("db1")
public class MysqlServiceImpl implements MysqlService {

    @Resource
    private UserInfoDao userInfoDao;

    @Override
    public UserInfoDO findById(Long id) {
        UserInfoPO userInfo = userInfoDao.getById(id);

        return MapperUtils.map(userInfo, UserInfoDO.class);
    }

    public List<UserInfoDO> getPageList(int page, int pageSize){
        List<UserInfoPO> userInfoList = userInfoDao.queryPageList(page, pageSize);
        return userInfoList.stream().map(userInfoPo -> MapperUtils.map(userInfoPo, UserInfoDO.class)).collect(Collectors.toList());
    }

    public Integer getPageCount(){
        return userInfoDao.queryPageCount();
    }

    /**
     * 使用db1
     * @param id
     */
    @Override
    @DS("db1")
    public void updateDb1(Long id) {
        UserInfoPO userInfoPO = new UserInfoPO();
        userInfoPO.setId(id);
        userInfoPO.setUserName("update1");
        userInfoPO.setUpdatedAt(new Date());
        userInfoDao.update(userInfoPO);
    }

    /**
     * 使用db2 方法注解优先于类注解
     * @param id
     */
    @Override
    @DS("db2")
    public void updateDb2(Long id) {
        UserInfoPO userInfoPO = new UserInfoPO();
        userInfoPO.setId(id);
        userInfoPO.setUserName("update2");
        userInfoPO.setUpdatedAt(new Date());
        userInfoDao.update(userInfoPO);
    }

    /**
     * 多源本地事务示例
     * @param id1
     * @param id2
     */
    @Override
    @DSTransactional
    public void update(Long id1, Long id2) {

        this.updateDb1(id1);
        this.updateDb1(id2);
    }
}
