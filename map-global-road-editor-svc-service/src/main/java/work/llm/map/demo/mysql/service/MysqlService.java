package work.llm.map.demo.mysql.service;

import work.llm.map.service.user.model.UserInfoDO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MysqlService {

    /**
     * 根据id查找
     * @param id
     * @return
     */
    UserInfoDO findById(Long id);

    /**
     * 获取分页列表
     * @param page
     * @param pageSize
     * @return
     */
    List<UserInfoDO> getPageList(int page, int pageSize);

    /**
     * 获取分页总数
     * @return
     */
    Integer getPageCount();

    /**
     * updateDb1
     * @param id
     */
    void updateDb1(Long id);

    /**
     * updateDb2
     * @param id
     */
    void updateDb2(Long id);

    /**
     * update
     * @param id1
     * @param id2
     */
    void update(Long id1, Long id2);
}