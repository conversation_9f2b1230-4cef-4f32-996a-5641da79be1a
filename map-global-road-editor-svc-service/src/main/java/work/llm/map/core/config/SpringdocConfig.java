package work.llm.map.core.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

@Configuration
public class SpringdocConfig {

    @Value("${server.servlet.context-path}")
    private String contextPath;

    @Value("${properties.area-interceptor.header-key-area}")
    private String areaHeaderKey;

    @Bean
    public OpenAPI springdocOpenAPI() {
        // Define the security scheme for the area header
        SecurityScheme areaHeaderScheme = new SecurityScheme()
                .type(SecurityScheme.Type.APIKEY)
                .in(SecurityScheme.In.HEADER)
                .name(areaHeaderKey)
                .description("区域头信息，用于指定操作的区域");

        // Create a security requirement with the area header
        SecurityRequirement securityRequirement = new SecurityRequirement().addList(areaHeaderKey);

        return new OpenAPI()
                .info(new Info()
                        .title("国际化道路编辑平台 API")
                        .description("全球道路编辑服务API文档")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Map Team")
                                .email("<EMAIL>")
                                .url("https://huolala.cn"))
                        .license(new License()
                                .name("Private License")
                                .url("https://huolala.cn")))
                .servers(Arrays.asList(
                        // new Server().url("/").description("Default Server"),
                        new Server().url(contextPath).description("API Server with Context Path")
                ))
                // Add the security scheme to the components
                .components(new Components().addSecuritySchemes(areaHeaderKey, areaHeaderScheme))
                // Add the security requirement to all operations
                .addSecurityItem(securityRequirement);
    }
}
