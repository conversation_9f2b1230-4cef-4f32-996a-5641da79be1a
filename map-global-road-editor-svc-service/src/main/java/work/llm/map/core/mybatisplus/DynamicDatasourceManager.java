package work.llm.map.core.mybatisplus;

import cn.lalaframework.dynamic.datasource.DynamicRoutingDataSource;
import cn.lalaframework.dynamic.datasource.creator.HikariDataSourceCreator;
import cn.lalaframework.dynamic.datasource.ds.ItemDataSource;
import cn.lalaframework.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import cn.lalaframework.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.sql.DataSource;

@Component
public class DynamicDatasourceManager {

    @Resource
    private DynamicRoutingDataSource dynamicRoutingDataSource; // dynamic-datasource 的核心数据源

    @Resource
    private HikariDataSourceCreator hikariDataSourceCreator; // 内置的数据源创建器

    /**
     * 动态添加数据源
     *
     * @param dsName   数据源名称（如 tenant_001）
     * @param property 连接属性（url, username, password 等）
     */
    public void add(String dsName, DataSourceProperty property) {
        DataSource dataSource = dynamicRoutingDataSource.getDataSource(dsName);
        if (dataSource instanceof ItemDataSource) {
            ItemDataSource itemDataSource = (ItemDataSource) dataSource;
            if (!itemDataSource.getName().equals("default")) return;
            DataSource ds = hikariDataSourceCreator.createDataSource(property);
            dynamicRoutingDataSource.addDataSource(dsName, ds);
        }
    }

    /**
     * 移除数据源
     */
    public void remove(String dsName) {
        if (dsName.equals(DynamicDataSourceContextHolder.peek())) return;
        DataSource dataSource = dynamicRoutingDataSource.getDataSource(dsName);
        if (dataSource instanceof ItemDataSource) {
            ItemDataSource itemDataSource = (ItemDataSource) dataSource;
            if (itemDataSource.getName().equals("default")) return;
        }
        dynamicRoutingDataSource.removeDataSource(dsName);
    }
}