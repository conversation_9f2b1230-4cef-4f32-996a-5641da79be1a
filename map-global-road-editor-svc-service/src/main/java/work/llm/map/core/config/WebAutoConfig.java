package work.llm.map.core.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import work.llm.map.core.interceptor.AreaInterceptor;
import work.llm.map.core.properties.AreaInterceptorProperties;

import javax.annotation.Resource;

@Configuration(proxyBeanMethods = false)
@EnableWebMvc
public class WebAutoConfig implements WebMvcConfigurer {

    @Resource
    private AreaInterceptorProperties areaInterceptorProperties;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new AreaInterceptor(areaInterceptorProperties))
                .excludePathPatterns(areaInterceptorProperties.getIgnoreUris());
    }
}
