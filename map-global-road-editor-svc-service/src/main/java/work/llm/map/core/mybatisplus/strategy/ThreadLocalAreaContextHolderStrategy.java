package work.llm.map.core.mybatisplus.strategy;

public class ThreadLocalAreaContextHolderStrategy implements AreaContextHolderStrategy {
    private static final InheritableThreadLocal<String> contextHolder = new InheritableThreadLocal<>();


    @Override
    public void clear() {
        contextHolder.remove();
    }

    @Override
    public String get() {
        return contextHolder.get();
    }

    @Override
    public void set(String schema) {
        contextHolder.set(schema);
    }
}
