package work.llm.map.core.toolkit;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.MediaType;
import work.llm.map.common.result.Result;

import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;


public class ResponseWriter {

    public static void write(HttpServletResponse response, Result<?> result) throws Exception {
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);

        PrintWriter writer = response.getWriter();
        ObjectMapper objectMapper = (ObjectMapper) SpringContainerGetter.getBean(ObjectMapper.class);
        EnDecryptKit encryptKit = new EnDecryptKit(true);
        writer.write(encryptKit.encrypt(objectMapper.writeValueAsString(result)));
        writer.flush();
    }
}
