package work.llm.map.core.toolkit;

import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * <AUTHOR>
 */
@Slf4j
@SuppressWarnings({"Duplicates"})
@NoArgsConstructor
public class EnDecryptKit {

    public EnDecryptKit(boolean defaultable) {
        if (defaultable) {
            setAeskey(aeskey);
            setBm(bm);
            setVipara(vipara);
        }
    }

    public String getVipara() {
        return vipara;
    }

    public String getBm() {
        return bm;
    }

    public String getAeskey() {
        return aeskey;
    }

    public void setAeskey(String aesKey) {
        aeskey = aesKey;
    }

    public void setVipara(String vipara) {
        EnDecryptKit.vipara = vipara;
    }

    public void setBm(String bm) {
        EnDecryptKit.bm = bm;
    }

    //初始向量(AES为16bytes,DES为8bytes)
    private static String vipara = "aabbccddeeffgghh";
    //编码方式
    private static String bm = "UTF-8";
    //私钥(AES固定格式为128/192/256 bits;即：16/24/32bytes;DES固定格式为128bits，即8bytes)
    private static String aeskey = "aabbccddeeffgghh";

    /**
     * 加密
     *
     * @param cleartext 明文
     * @return 密文
     */
    public String encrypt(String cleartext) {
        try {
            return encrypt(cleartext.getBytes(bm));
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
            return "";
        }
    }

    public String encrypt(byte[] bytes) {
        //加密方式： AES128(CBC/PKCS5Padding) + Base64, 私钥：aabbccddeeffgghh
//        try {
        IvParameterSpec zeroIv = new IvParameterSpec(vipara.getBytes());
        //两个参数，第一个为私钥字节数组， 第二个为加密方式 AES或者DES
        SecretKeySpec key = new SecretKeySpec(aeskey.getBytes(), "AES");
        //实例化加密类，参数为加密方式，要写全(PKCS5Padding比PKCS7Padding效率高，PKCS7Padding可支持IOS加解密)
        Cipher cipher;
        try {
            cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");

            //初始化，此方法可以采用三种方式，按加密算法要求来添加。（1）无第三个参数（2）第三个参数为SecureRandom random = new SecureRandom();中random对象，随机数。(AES不可采用这种方法)（3）采用此代码中的IVParameterSpec
            cipher.init(Cipher.ENCRYPT_MODE, key, zeroIv);
            //加密操作,返回加密后的字节数组，然后需要编码。主要编解码方式有Base64, HEX, UUE,7bit等等。此处看服务器需要什么编码方式
            byte[] encryptedData = cipher.doFinal(bytes);
            Base64.Encoder encoder = Base64.getEncoder();
            return encoder.encodeToString(encryptedData);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException |
                 InvalidAlgorithmParameterException | IllegalBlockSizeException | BadPaddingException e) {
            log.error(e.getMessage(), e);
            return "";
        }
    }

    public static String encrypt(String cleartext, String vipara, String aeskey, String bm) {
        //加密方式： AES128(CBC/PKCS5Padding) + Base64, 私钥：aabbccddeeffgghh
        try {
            IvParameterSpec zeroIv = new IvParameterSpec(vipara.getBytes());
            //两个参数，第一个为私钥字节数组， 第二个为加密方式 AES或者DES
            SecretKeySpec key = new SecretKeySpec(aeskey.getBytes(), "AES");
            //实例化加密类，参数为加密方式，要写全(PKCS5Padding比PKCS7Padding效率高，PKCS7Padding可支持IOS加解密)
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            //初始化，此方法可以采用三种方式，按加密算法要求来添加。（1）无第三个参数（2）第三个参数为SecureRandom random = new SecureRandom();中random对象，随机数。(AES不可采用这种方法)（3）采用此代码中的IVParameterSpec
            cipher.init(Cipher.ENCRYPT_MODE, key, zeroIv);
            //加密操作,返回加密后的字节数组，然后需要编码。主要编解码方式有Base64, HEX, UUE,7bit等等。此处看服务器需要什么编码方式
            byte[] encryptedData = cipher.doFinal(cleartext.getBytes(bm));

            Base64.Encoder encoder = Base64.getEncoder();
            return encoder.encodeToString(encryptedData);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return "";
        }
    }

    /**
     * 解密
     *
     * @param encrypted 密文
     * @return 明文
     */
    public String decrypt(String encrypted) {
        try {
            Base64.Decoder decoder = Base64.getMimeDecoder();
            byte[] byteMi = decoder.decode(encrypted);
            IvParameterSpec zeroIv = new IvParameterSpec(vipara.getBytes());
            SecretKeySpec key = new SecretKeySpec(aeskey.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            //与加密时不同MODE:Cipher.DECRYPT_MODE
            cipher.init(Cipher.DECRYPT_MODE, key, zeroIv);
            byte[] decryptedData = cipher.doFinal(byteMi);
            return new String(decryptedData, bm);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return "";
        }
    }

    public static String decrypt(String encrypted, String vipara, String aeskey, String bm) {
        try {
            Base64.Decoder decoder = Base64.getDecoder();
            byte[] byteMi = decoder.decode(encrypted);

            IvParameterSpec zeroIv = new IvParameterSpec(vipara.getBytes());
            SecretKeySpec key = new SecretKeySpec(aeskey.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            //与加密时不同MODE:Cipher.DECRYPT_MODE
            cipher.init(Cipher.DECRYPT_MODE, key, zeroIv);
            byte[] decryptedData = cipher.doFinal(byteMi);
            return new String(decryptedData, bm);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return "";
        }
    }

    /**
     * 测试
     *
     * @param args 参数
     */
    public static void main(String[] args) {
        EnDecryptKit enDecryptKit = new EnDecryptKit();
        //String content = "资料MeshLinkTemplate打断：MeshLinkTemplate{hllLinkid='702311462954369024', dLinkid='702311462954369024', hllSNid='null', hllENid='null', kind=8, formway='1', dir=1, app=1, toll=0, adopt=2, md=0, devs=2, spet=0, funct=5, urban=0, pave=1, laneN=2, laneL=1, laneR=1, laneC=2, width='55', viad=2, lAdmin='null', rAdmin='null', geom='LINESTRING (119.575256 25.870728, 119.57606 25.869992)', len=114, fSpeed='150,0,1', tSpeed='150,0,1', spClass='7', diciType=0, verifyflag=2, preLaunch='null', nameChO='null', nameChA='null', nameChF='null', namePhO='null', namePhA='null', namePhF='null', nameEnO='null', nameEnA='null', nameEnF='null', namePo='null', nameCht='null', codeType=0, nameType=0, srcFlag=0, meshId='385964', memo='null', cp='null', datasource='9', upDate=2022-01-12T10:24:44.651158, status=0, collectVersion='V_1', workStatus=0, subId='21', taskId='null'} ,挂接MeshLink: MeshLink{id=1192805, hllLinkid='72057594091961415', dLinkid='54033479', hllSNid='72057594056929440', hllENid='72057594038371489', kind=3, formway='81', dir=2, app=1, toll=2, adopt=null, md=1, devs=1, spet=0, funct=3, urban=0, pave=1, laneN=4, laneL=4, laneR=0, laneC=3, width='130', viad=0, lAdmin='350112', rAdmin='350112', geom='LINESTRING (119.57726 25.87106, 119.57687 25.8707, 119.57616 25.87008, 119.57606 25.869992, 119.57553 25.86952)', len=243.49000549316406, fSpeed='600,1,', tSpeed='null', spClass='5', diciType=0, verifyflag=2, preLaunch='null', nameChO='Ｇ２２８', nameChA='null', nameChF='null', namePhO='G228', namePhA='null', namePhF='null', nameEnO='G228', nameEnA='null', nameEnF='null', namePo='null', nameCht='null', codeType=1, nameType=0, srcFlag=6, meshId='385964', memo='null', cp='fujian', datasource='1', upDate=2021-11-04T06:16:58, status=0, linkSource='null', linkDestination='null', collectVersion='V_1', checkStatus=0, changeStatus=0, taskId='4242'}";
        String content = "{\n" +
                         "  \"taskId\": \"538\",\n" +
                         "  \"commitType\": \"6\"\n" +
                         "}";
        // 加密
        System.out.println("加密前：" + content);
        String encryptResult = enDecryptKit.encrypt(content);

        System.out.println("加密后：" + encryptResult);
        // 解密
        //String decryptResult = enDecryptKit.decrypt(encryptResult);
        String decryptResult = enDecryptKit.decrypt("c4KqmqZqJBJjbKF/Hx5hxIDUY/bllbQJDEyYh4APO9FKswrqxsY7O0KFIrE1BAA/6ZviN+FGjsw2KLzwyDvyXVnFTmWVSAQSGQVCHeg/tSnY+Lp3R6n9IZ1QXAdLEnUYR0NybcX874TopY9VE+30A9YJsxwvt4jkvCh/lrriSeBX6/QX+X8Hh5DcNJh1YUwuQn/ygjyXklYNiN8YY4lBhSr4tNt7a1asHZ5G7e+Uv+KjOpx6biyKH1hksa3fU/xm0bpjiWiMeBa4M8owKSsY9LupkqCHvix6tOnCc+QeAezSc6sBLSLbvovFYBSo4KKmxyz1bsHhr/TXGXIUin+ASTbffGKqRMtfDu+6hmumCg44tR3wUE2SgT4zFoHGMfaFTW2kNkcPimoGDjoPVS1BRwZFJ5fbwT3zGnnGmYO0KYDx22LmhJPGTbtDEBwqJQa4vfmA7TbWRJcZanz0bT4eByWIHIRyljQyndKcnHjcnWOQ2eO3KgKSMJqZVCrlJQt/mx5ULxXQv73k4cR7HnP9IkCDS4/inwhiq/A8w+Dc33q2NtTSZlCZUoldfLnQ5EZAFNX09jsT4obAyq+VJV8iAKjkPCzZNdm0O0FVXGL4d+z/Vfqfj82hIcUdwGMwHD0tvuuCknbZHzCwtXmpSRe2ygmLc6xuw7k03Wv/R/jDNv51z+CAVTXjv/OYss2g1x6U97HgCp8WdxcNoow92frX8FviC6pAz1eMSNBV74+XZ7XvBDwuekY54Sz2eU5kHZ1hV72p4h+gb212mL1wQpHAuRnN6dGFls8buQtBFDyv/A8l0nD6/GFGDrWzIPh6Ougz");
        System.out.println("解密后：" + decryptResult);
    }
}
