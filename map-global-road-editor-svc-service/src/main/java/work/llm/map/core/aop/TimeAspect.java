package work.llm.map.core.aop;

import com.google.common.base.Stopwatch;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

@Component
@Aspect
public class TimeAspect {

    private static final Logger logger = LoggerFactory.getLogger(TimeAspect.class);

    @Pointcut("within(@org.springframework.web.bind.annotation.RestController *)")
    public void pointcut() {
    }

    @Around("pointcut()")
    public Object timing(ProceedingJoinPoint joinPoint) throws Throwable {
        Object object;
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // Get request information if available
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            String url = "Unknown URL";

            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                url = request.getRequestURL().toString();
            } else {
                // Log method name if request attributes are not available
                url = joinPoint.getSignature().toShortString();
            }

            // Execute the method
            object = joinPoint.proceed();

            // Log the execution time
            logger.info(" ========> URL [" + url + "] TAKES " + stopwatch.elapsed(TimeUnit.MILLISECONDS) + " ms.");
        } finally {
            stopwatch.stop();
        }

        return object;
    }
}
