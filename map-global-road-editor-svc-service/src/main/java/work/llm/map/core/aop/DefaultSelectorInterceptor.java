package work.llm.map.core.aop;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Aspect
@Component
@Slf4j
public class DefaultSelectorInterceptor {

    @Resource
    private DefaultSelectorImpl defaultSelector;

    @Pointcut("@annotation(work.llm.map.core.aop.DefaultSelector)")
    @SuppressWarnings("unused")
    public void pointcut() {
    }

    @Around("pointcut()")
    public Object intercept(ProceedingJoinPoint joinPoint) throws Throwable {
        Object object;
        String ds = defaultSelector.select();
        try {
            object = joinPoint.proceed();
        } finally {
            defaultSelector.recover(ds);
        }
        return object;
    }
}
