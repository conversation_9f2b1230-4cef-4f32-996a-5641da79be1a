package work.llm.map.core.interceptor;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import work.llm.map.common.exception.BizErrorCode;
import work.llm.map.common.result.Result;
import work.llm.map.core.mybatisplus.AreaContextHolder;
import work.llm.map.core.properties.AreaInterceptorProperties;
import work.llm.map.core.toolkit.ResponseWriter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@AllArgsConstructor
@SuppressWarnings("all")
public class AreaInterceptor implements HandlerInterceptor {

    private final AreaInterceptorProperties areaInterceptorProperties;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String schema = request.getHeader(areaInterceptorProperties.getHeaderKeyArea());
        if (StringUtils.isBlank(schema)) {
            log.info("URL={}", request.getRequestURL());
            ResponseWriter.write(response, Result.fail(BizErrorCode.VALIDATE_FAIL.getRet(), "area header is required"));
            return false;
        }

        AreaContextHolder.set(schema);

        return HandlerInterceptor.super.preHandle(request, response, handler);
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        AreaContextHolder.clear();
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        AreaContextHolder.clear();
    }
}
