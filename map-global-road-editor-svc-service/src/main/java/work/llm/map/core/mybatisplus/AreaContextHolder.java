package work.llm.map.core.mybatisplus;

import org.apache.commons.lang3.StringUtils;
import work.llm.map.core.mybatisplus.strategy.AreaContextHolderStrategy;
import work.llm.map.core.mybatisplus.strategy.ThreadLocalAreaContextHolderStrategy;

import java.util.HashMap;
import java.util.Map;

public class AreaContextHolder {

    private static AreaContextHolderStrategy strategy;
    private static final Map<String, AreaContextHolderStrategy> strategies = new HashMap<>();

    static {
        initialize();
    }

    private static void initialize() {
        initializeStrategies();
        initializeStrategy();
    }

    private static void initializeStrategies() {
        strategies.put("default", new ThreadLocalAreaContextHolderStrategy());
    }

    private static void initializeStrategy() {
        String strategy = System.getProperty("area.context.holder.strategy");
        AreaContextHolder.strategy = StringUtils.isBlank(strategy) ? strategies.get("default") : strategies.get(strategy);
    }

    public static void clear() {
        strategy.clear();
    }

    public static String get() {
        return strategy.get();
    }

    public static void set(String schema) {
        strategy.set(schema);
    }
}
