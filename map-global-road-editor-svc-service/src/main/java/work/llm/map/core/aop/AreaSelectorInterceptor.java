package work.llm.map.core.aop;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Aspect
@Component
@Slf4j
public class AreaSelectorInterceptor {

    @Resource
    private AreaSelectorImpl areaSelector;

    @Pointcut("@annotation(work.llm.map.core.aop.AreaSelector)")
    @SuppressWarnings("unused")
    public void pointcut() {
    }

    @Around("pointcut()")
    public Object intercept(ProceedingJoinPoint joinPoint) throws Throwable {
        Object object;
        String ds = areaSelector.select();
        try {
            object = joinPoint.proceed();
        } finally {
            areaSelector.recover(ds);
        }
        return object;
    }
}
