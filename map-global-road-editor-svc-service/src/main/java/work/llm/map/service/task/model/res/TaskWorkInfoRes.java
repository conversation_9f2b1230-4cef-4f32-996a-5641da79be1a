package work.llm.map.service.task.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TaskWorkInfoRes {

    @Builder.Default
    @Schema(description = "未作业数量")
    private Long undoNumber = 0L;
    @Schema(description = "质检正确率")
    private String qualityAccuracy;
    @Schema(description = "验收正确率")
    private String verifyAccuracy;
    @Schema(description = "资料展示数量")
    private String displayNumber;
}
