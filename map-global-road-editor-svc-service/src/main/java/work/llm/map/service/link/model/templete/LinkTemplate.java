package work.llm.map.service.link.model.templete;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class LinkTemplate {

    private Long hllLinkid;
    private Long hllSNid;
    private Long hllENid;
    private String kind = "8";
    private String formway = "1";
    private String dir = "1";
    private String funct = "5";
    private String app = "1";
    private String toll = "2";
    private String md = "0";
    private String urban = "0";
    private String pave = "1";
    private Integer laneN = 0;
    private Integer laneL = 0;
    private Integer laneR = 0;
    private String laneC = "2";
    private String spClass = "7";
    private String divider = "N";
    private String dividerLeg = "N";
    private String pubAccess = "Y";
    private Integer status = 3;
}