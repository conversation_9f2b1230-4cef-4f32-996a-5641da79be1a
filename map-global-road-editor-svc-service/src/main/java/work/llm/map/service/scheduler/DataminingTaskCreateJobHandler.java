package work.llm.map.service.scheduler;

import cn.huolala.arch.hermes.api.annotation.HermesReference;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.lalaframework.dynamic.datasource.annotation.DSTransactional;
import cn.lalaframework.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import cn.lalaframework.job.sdk.HllXxlJobManager;
import cn.lalaframework.job.sdk.annotation.HllXxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import work.llm.map.common.constant.Const;
import work.llm.map.common.utils.JsonUtils;
import work.llm.map.common.utils.MapperUtils;
import work.llm.map.dao.common.entity.TaskCreator;
import work.llm.map.dao.datamining.entity.DataminingGroup;
import work.llm.map.facade.model.RoadCreateTaskRequest;
import work.llm.map.facade.service.RoadTaskEditFacade;
import work.llm.map.service.datamining.DataminingGroupService;
import work.llm.map.service.task.TaskService;
import work.llm.map.service.task.model.req.TaskCreateReq;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@SuppressWarnings({"Duplicates", "unused"})
public class DataminingTaskCreateJobHandler {

    @Resource
    private DataminingGroupService groupService;
    @Resource
    private TaskService taskService;
    @HermesReference(value = "${inner.facade.task}")
    private RoadTaskEditFacade roadTaskEditFacade;

    @HllXxlJob(value = "dataminingTaskCreateJobHandler")
    @DSTransactional
    public void execute() {
        String param = HllXxlJobManager.getJobParam();
        if (StrUtil.isEmpty(param)) return;

        String[] parameters = param.split(Const.SYMBOL_COMMA);
        for (String parameter : parameters) {
            String old = DynamicDataSourceContextHolder.peek();
            try {
                DynamicDataSourceContextHolder.push(parameter);

                List<TaskCreator> tasks = groupService.getBaseMapper().taskCreatable();
                if (CollUtil.isEmpty(tasks)) continue;

                for (TaskCreator taskCreator : tasks) {
                    try {
                        List<DataminingGroup> groups = this.groupService.lambdaQuery().eq(DataminingGroup::getTaskCreated, Const.NO)
                                .eq(DataminingGroup::getDsId, taskCreator.getDsId()).list();
                        if (CollUtil.isEmpty(groups)) continue;
                        List<String> tileIds = groups.stream().map(DataminingGroup::getTileId).distinct().collect(Collectors.toList());
                        taskCreator.setTileId(CollUtil.join(tileIds, ","));
                        taskCreator.setVersion(LocalDate.now().toString());

                        String taskId = createTask(taskCreator);
                        if (StrUtil.isEmpty(taskId)
                            || taskId.equalsIgnoreCase("null")) continue;
                        // 设置任务id
                        taskCreator.setTaskId(taskId);
                        this.taskService.create(MapperUtils.map(taskCreator, TaskCreateReq.class));
                        // 更新资料状态
                        this.groupService.lambdaUpdate()
                                .eq(DataminingGroup::getCollectVersion, taskCreator.getCollectVersion())
                                .eq(DataminingGroup::getDsId, taskCreator.getDsId())
                                .eq(DataminingGroup::getTaskCreated, Const.NO)
                                .set(DataminingGroup::getTaskCreated, Const.YES)
                                .set(DataminingGroup::getTaskCreateTime, LocalDateTime.now())
                                .set(DataminingGroup::getUpdateTime, LocalDateTime.now())
                                .set(DataminingGroup::getTaskId, taskId)
                                .update();
                    } catch (Exception e) {
                        log.error("创建任务失败,collect_version=[{}],ds_id=[{}]", taskCreator.getCollectVersion(), taskCreator.getDsId(), e);
                    }
                }
            } finally {
                if (StrUtil.isNotBlank(old)) DynamicDataSourceContextHolder.push(old);
            }
        }

    }

    private String createTask(TaskCreator taskCreator) {
        RoadCreateTaskRequest request = MapperUtils.map(taskCreator, RoadCreateTaskRequest.class);
        log.info("创建任务请求参数,request=[{}]", JsonUtils.toJsonString(request));
        Long id = roadTaskEditFacade.create(request);
        return String.valueOf(id);
    }
}
