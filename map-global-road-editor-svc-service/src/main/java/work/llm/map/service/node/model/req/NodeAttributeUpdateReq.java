package work.llm.map.service.node.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import work.llm.map.dao.node.entity.Node;
import work.llm.map.service.common.DatasourceAndTaskIdReq;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "节点属性修改请求")
public class NodeAttributeUpdateReq extends DatasourceAndTaskIdReq {

    @Schema(description = "节点属性")
    private Node node;
}
