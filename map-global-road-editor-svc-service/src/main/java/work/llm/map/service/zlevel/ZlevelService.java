package work.llm.map.service.zlevel;

import cn.hutool.core.collection.CollUtil;
import cn.lalaframework.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vividsolutions.jts.geom.Geometry;
import com.vividsolutions.jts.operation.distance.DistanceOp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import work.llm.map.common.constant.Const;
import work.llm.map.dao.link.entity.Link;
import work.llm.map.dao.zlevel.entity.Zlevel;
import work.llm.map.dao.zlevel.mapper.ZlevelMapper;
import work.llm.map.service.common.CommonService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class ZlevelService extends ServiceImpl<ZlevelMapper, Zlevel> {

    @Resource
    private CommonService commonService;

    @DSTransactional
    public void split(String hllLinkid, Link link1, Link link2) {
        log.info("进入打断zlevel逻辑，参数hllLinkid=[{}], taskId=[{}]", hllLinkid, link1.getTaskId());

        List<Zlevel> zlevels = this.lambdaQuery().eq(Zlevel::getHllLinkid, hllLinkid).ne(Zlevel::getStatus, Const.STATUS_DELETED).list();
        if (CollUtil.isEmpty(zlevels)) return;

        log.info("需要打断[{}]条zlevel数据:{}", zlevels.size(), Arrays.toString(zlevels.toArray()));

        String datasource = link1.getDatasource();
        String taskId = link1.getTaskId();
        LocalDateTime upDate = link1.getUpDate();

        Geometry point;
        double distance;
        double minimum;
        List<Link> links = Arrays.asList(link1, link2);
        Link nearest = null;
        for (Zlevel zlevel : zlevels) {
            point = zlevel.getGeometry();
            minimum = Double.MAX_VALUE;
            for (Link link : links) {
                distance = new DistanceOp(point, link.getGeometry()).distance();
                if (distance <= minimum) {
                    minimum = distance;
                    nearest = link;
                }
            }
            if (Objects.isNull(nearest)) continue;
            zlevel.setHllLinkid(nearest.getHllLinkid());

            zlevel.setUpDate(upDate);
            zlevel.setStatus(Const.STATUS_UPDATED);
            zlevel.setTaskId(taskId);
            zlevel.setDatasource(commonService.datasource(datasource, zlevel.getDatasource()));
            this.updateById(zlevel);
        }

        log.info("处理zlevel逻辑结束,参数=>hllLinkid=[{}],taskId=[{}]", hllLinkid, link1.getTaskId());
    }

    @DSTransactional
    public boolean update(String hllLinkid, String taskId, String datasource, Integer status) {
        log.info("关联更新zlevel逻辑，参数hllLinkid=[{}], taskId=[{}]", hllLinkid, taskId);
        LocalDateTime now = LocalDateTime.now();
        List<Zlevel> zlevels = this.lambdaQuery().ne(Zlevel::getStatus, Const.STATUS_DELETED)
                .eq(Zlevel::getHllLinkid, hllLinkid).list();
        if (CollUtil.isEmpty(zlevels)) return true;

        log.info("关联更新[{}]条zlevel数据:{}", zlevels.size(), Arrays.toString(zlevels.toArray()));

        for (Zlevel zlevel : zlevels) {
            zlevel.setTaskId(taskId);
            zlevel.setDatasource(commonService.datasource(datasource, zlevel.getDatasource()));
            zlevel.setStatus(status);
            zlevel.setUpDate(now);
            this.updateById(zlevel);
        }
        return true;
    }
}
