package work.llm.map.service.link.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import work.llm.map.dao.link.entity.Link;
import work.llm.map.dao.node.entity.Node;
import work.llm.map.service.common.DatasourceAndTaskIdReq;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "拓扑分离请求")
public class LinkTopologySeparateReq extends DatasourceAndTaskIdReq {

    @Schema(description = "被分离道路信息")
    private Link link;
    @Schema(description = "移动点信息")
    private Node moveNode;
    @Schema(description = "移动道路信息")
    private Link moveLink;
}
