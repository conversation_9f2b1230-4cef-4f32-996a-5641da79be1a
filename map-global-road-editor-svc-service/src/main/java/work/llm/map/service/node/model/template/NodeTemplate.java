package work.llm.map.service.node.model.template;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.LocalDateTime;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class NodeTemplate {

    private String hllNodeid;
    private String kind = "10ff";
    private String type = "0";
    private String datasource;
    private Integer status = 3;
    private String geom;
    private LocalDateTime upDate = LocalDateTime.now();
}
