package work.llm.map.service.quality;

import cn.lalaframework.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import work.llm.map.common.constant.Const;
import work.llm.map.common.exception.BizException;
import work.llm.map.dao.datamining.entity.DataminingGroup;
import work.llm.map.dao.matlink.entity.MatLink;
import work.llm.map.dao.quality.entity.Quality;
import work.llm.map.dao.quality.mapper.QualityMapper;
import work.llm.map.dao.task.entity.Task;
import work.llm.map.service.common.CommonService;
import work.llm.map.service.datamining.DataminingService;
import work.llm.map.service.matlink.MatLinkService;
import work.llm.map.service.quality.model.dto.QualityStatisticDTO;
import work.llm.map.service.quality.model.req.QualityPageReq;
import work.llm.map.service.quality.model.req.QualityUpdateReq;
import work.llm.map.service.quality.model.res.QualityPageRes;
import work.llm.map.service.task.TaskService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@SuppressWarnings("all")
public class QualityService extends ServiceImpl<QualityMapper, Quality> {

    @Resource
    private CommonService commonService;
    @Resource
    private MatLinkService matLinkService;
    @Resource
    private DataminingService dataminingService;
    @Resource
    private TaskService taskService;

    public QualityPageRes queryPage(QualityPageReq req) {
        LambdaQueryChainWrapper<Quality> lambdaQuery = this.lambdaQuery();

        if (req.qc()) {
            lambdaQuery.orderByAsc(Quality::getQualityStatus)
                    .orderByAsc(Quality::getTag)
                    .orderByAsc(Quality::getQualityId);
        } else if (req.ew()) {
            lambdaQuery.orderByAsc(Quality::getTag)
                    .orderByAsc(Quality::getQualityStatus)
                    .orderByAsc(Quality::getQualityId);
        }

        Page<Quality> page = lambdaQuery.eq(Quality::getTaskId, req.getTaskId())
                .page(new Page<>(req.getPageNum(), req.getPageSize()));
        QualityPageRes res = new QualityPageRes();
        res.setQualityPage(page);

        Integer count = this.lambdaQuery().eq(Quality::getTaskId, req.getTaskId())
                .eq(Quality::getQualityStatus, Const.QUALITY_STATUS_UNQUALIFIED).count();
        res.setQualityNumber(count);

        return res;
    }

    public Quality queryByMat(String taskId, Long matId) {
        return this.lambdaQuery()
                .eq(Quality::getTaskId, taskId)
                .eq(Quality::getMatId, matId)
                .one();
    }

    @DSTransactional
    public String update(QualityUpdateReq req) {
        String taskId = req.getTaskId();
        this.lambdaUpdate()
                .eq(Quality::getQualityId, req.getQualityId())
                .eq(Quality::getTaskId, taskId)
                .set(Quality::getQualityStatus, req.getQualityStatus())
                .set(Quality::getTag, req.getTag())
                .set(Quality::getDescription, req.getDescription())
                .set(Quality::getConfirmStatus, req.getConfirmStatus())
                .set(Quality::getModifyStatus, req.getModifyStatus())
                .set(Quality::getMemo, req.getMemo())
                .set(Quality::getUpdateTime, LocalDateTime.now())
                .update();
        // 计算正确率
        BigDecimal accuracy = this.statistic(taskId).getAccuracyRatio();
        return accuracy.toString();
    }

    public QualityStatisticDTO statistic(String taskId) {
        QualityStatisticDTO statistic = new QualityStatisticDTO();
        List<Quality> qualities = this.lambdaQuery().eq(Quality::getTaskId, taskId).list();
        statistic.setTotalNumber(qualities.size());
        if (CollectionUtils.isEmpty(qualities)) return statistic;
        // 分母数，质检状态非3-无的个数
        qualities = qualities.stream()
                .filter(q -> q.getQualityStatus() != Const.QUALITY_STATUS_NO).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(qualities)) return statistic;

        // 分母（多错漏正）
        BigDecimal denominator = new BigDecimal(qualities.size()).setScale(2, RoundingMode.HALF_UP);
        // 分子（正）
        List<Quality> corrects = qualities.stream()
                .filter(q -> q.getQualityStatus() == Const.QUALITY_STATUS_QUALIFIED && q.getTag() == Const.TAG_CORRECT).collect(Collectors.toList());
        BigDecimal numerator = new BigDecimal(corrects.size()).setScale(2, RoundingMode.HALF_UP);
        statistic.setCorrectNumber(numerator.intValue());
        statistic.setErrorNumber(qualities.size() - corrects.size());

        BigDecimal accuracyRatio = numerator.divide(denominator, 2, RoundingMode.HALF_UP);
        statistic.setAccuracyRatio(accuracyRatio);
        return statistic;
    }

    @DSTransactional
    public int extractAndCheck(String taskId, String extractRatio) {
        Task task = taskService.lambdaQuery().eq(Task::getTaskId, taskId)
                .last("for update").one();
        if (Objects.isNull(task)) throw new BizException("The specified task does not exist");
        int extractNumber = this.extract(task, extractRatio);
        if (extractNumber <= 0)
            throw new BizException("Failed to start quality check: No items were selected for QC. Please check the QC ratio and retry.");
        return extractNumber;
    }

    @DSTransactional
    public int extract(Task task, String extractRatio) {
        String taskId = task.getTaskId();
        // 当前任务的质检状态
        Integer qualityStatus = task.getQualityStatus();
        // 查询质检数据
        List<Quality> qualities = this.lambdaQuery().eq(Quality::getTaskId, taskId).list();
        // 获取资料
        List<?> mats = this.mats(task);
        int total = mats.size();
        // 计算抽取数量
        int extractNumber = commonService.calculateExtractNumber(total, extractRatio);

        // 如果已经开始质检说明已经抽取完成不能重复抽取
        if (qualityStatus.equals(Const.TASK_QUALITY_STATUS_STARTED)) return extractNumber;

        // 如果是返修直接返回当时抽取的数量
        if (qualityStatus.equals(Const.TASK_QUALITY_STATUS_REPAIR)) {
            return qualities.size();
        }

        // 计算抽取随机数
        Set<Integer> randoms = commonService.calculateRandoms(total, extractNumber);

        // 如果是0-默认则从资料抽取
        if (qualityStatus == Const.TASK_QUALITY_STATUS_DEFAULT) {
            // 抽取资料
            doExtract(mats, randoms);
            return extractNumber;
        }
        // 返工则需要重新抽取
        if (qualityStatus == Const.TASK_QUALITY_STATUS_REWORK) {
            // 重新抽取
            redoExtract(qualities, randoms);
            return extractNumber;
        }
        return 0;
    }

    @DSTransactional
    public void redoExtract(List<Quality> qualities, Set<Integer> randoms) {
        // 设置未质检
        List<Quality> extractedQualities = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (int i = 0; i < qualities.size(); i++) {
            Quality quality = qualities.get(i);

            quality.setUpdateTime(now);
            quality.setConfirmStatus(Const.QUALITY_CONFIRM_STATUS_NO); // 默认空
            quality.setTag(Const.TAG_NO); // 默认空
            quality.setModifyStatus(Const.MODIFY_STATUS_UNMODIFIED); // 默认未修改
            quality.setQualityStatus(Const.QUALITY_STATUS_NO);
            if (randoms.contains(i)) {
                quality.setQualityStatus(Const.QUALITY_STATUS_UNQUALIFIED); // 待质检
            }

            extractedQualities.add(quality);

            if (extractedQualities.size() >= 2000) {
                this.updateBatchById(extractedQualities);
                extractedQualities.clear();
            }
        }

        // 插入数据
        if (extractedQualities.isEmpty()) return;
        this.updateBatchById(extractedQualities);
    }

    @DSTransactional
    public void doExtract(List<?> mats, Set<Integer> randoms) {
        List<Quality> qualities = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (int i = 0; i < mats.size(); i++) {
            Object o = mats.get(i);
            Quality quality = new Quality();
            if (o instanceof MatLink) {
                MatLink matLink = (MatLink) o;
                quality.setMatId(matLink.getMatId());
                quality.setGeom(matLink.getGeometry());
                quality.setTaskId(matLink.getTaskId());
            } else if (o instanceof DataminingGroup) {
                DataminingGroup group = (DataminingGroup) o;
                quality.setMatId(group.getGroupId());
                quality.setGeom(group.getDgs().get(0).getGeometry());
                quality.setTaskId(group.getTaskId());
            }
            quality.setCheckAttribute(Const.TASK_TYPE_ROAD);
            quality.setCreateTime(now);
            quality.setUpdateTime(quality.getCreateTime());

            quality.setConfirmStatus(Const.QUALITY_CONFIRM_STATUS_NO); // 默认空
            quality.setTag(Const.TAG_NO); // 默认空
            quality.setModifyStatus(Const.MODIFY_STATUS_UNMODIFIED); // 默认未修改
            quality.setQualityStatus(Const.QUALITY_STATUS_NO);
            if (randoms.contains(i)) {
                quality.setQualityStatus(Const.QUALITY_STATUS_UNQUALIFIED); // 待质检
            }
            qualities.add(quality);

            if (qualities.size() >= 2000) {
                this.saveBatch(qualities);
                qualities.clear();
            }
        }
        // 插入数据
        if (!qualities.isEmpty()) this.saveBatch(qualities);
    }

    public void qualitySubmitCheck(String taskId) {
        Integer unqualifiedNumber = this.lambdaQuery().eq(Quality::getTaskId, taskId)
                .eq(Quality::getQualityStatus, Const.QUALITY_STATUS_UNQUALIFIED).count();
        if (unqualifiedNumber > 0)
            throw new BizException("There are unqualified items to be inspected, please submit after completion.");
    }

    private List<?> mats(Task task) {
        String taskId = task.getTaskId();
        if (taskService.isAttribute(task.getSubType())) {
            return dataminingService.startQualityCheck(taskId);
        } else {
            return matLinkService.startQualityCheck(taskId);
        }
    }
}
