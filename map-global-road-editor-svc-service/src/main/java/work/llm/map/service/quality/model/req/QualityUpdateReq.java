package work.llm.map.service.quality.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Schema(description = "质检修改请求参数")
public class QualityUpdateReq {

    @Schema(description = "质检ID")
    private Long qualityId;
    @Schema(description = "任务ID")
    private String taskId;
    @Schema(description = "修改备注")
    private String memo;
    @Schema(description = "作业员修改状态 1-未修改 2-已修改 3-确认不修改 4-无")
    private Integer modifyStatus;
    @Schema(description = "质检员修改质检标 1-多 2-错 3-漏 4-正 5-无")
    private Integer tag;
    @Schema(description = "质检标错误描述")
    private String description;
    @Schema(description = "质检员确认状态 1-确认正确 2-确认错误")
    private Integer confirmStatus;
    @Schema(description = "质检状态 1-未质检 2-已质检 3-无")
    private Integer qualityStatus;
}