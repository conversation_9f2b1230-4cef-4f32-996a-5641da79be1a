package work.llm.map.service.user;

import work.llm.map.service.user.model.UserInfoDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/1
 */
public interface UserInfoService {

	/**
	 * 获取用户信息
	 * @param id id
	 * @return {@link UserInfoDO}
	 */
	UserInfoDO getById(Long id);

	/**
	 * 获取分页列表
	 * @param page
	 * @param pageSize
	 * @return
	 */
	List<UserInfoDO> getPageList(int page, int pageSize);

	/**
	 * 获取分页总数
	 * @return
	 */
	Integer getPageCount();
}
