package work.llm.map.service.task;

import cn.lalaframework.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import work.llm.map.common.constant.Const;
import work.llm.map.common.exception.BizException;
import work.llm.map.dao.task.entity.Task;
import work.llm.map.dao.task.mapper.TaskMapper;
import work.llm.map.service.datamining.DataminingService;
import work.llm.map.service.matlink.MatLinkService;
import work.llm.map.service.quality.QualityService;
import work.llm.map.service.quality.model.dto.QualityStatisticDTO;
import work.llm.map.service.task.model.req.TaskCreateReq;
import work.llm.map.service.task.model.req.TaskPrecommitReq;
import work.llm.map.service.task.model.res.TaskPrecommitRes;
import work.llm.map.service.task.model.res.TaskWorkInfoRes;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

@Service
public class TaskService extends ServiceImpl<TaskMapper, Task> {

    @Resource
    private MatLinkService matLinkService;
    @Resource
    private DataminingService dataminingService;
    @Resource
    private QualityService qualityService;
    @Resource
    private TaskService taskService;

    @DSTransactional
    public TaskPrecommitRes precommit(TaskPrecommitReq req) {
        String taskId = req.getTaskId();
        // 如果资料任务不存在说明是检查项任务临时处理下，不需要做任何校验
        Task task = this.taskService.lambdaQuery().eq(Task::getTaskId, taskId).one();

        Integer commitType = req.getCommitType();
        switch (commitType) {
            case Const.COMMIT_TYPE_WORK_SUBMIT: {
                if (Objects.isNull(task)) return TaskPrecommitRes.builder().build();
                if (isAttribute(task.getSubType())) {
                    dataminingService.workSubmitCheck(taskId);
                    return TaskPrecommitRes.builder().build();
                }
                matLinkService.workSubmitCheck(taskId);
                BigDecimal effectiveRate = matLinkService.calculateEffectiveRate(taskId);
                return TaskPrecommitRes.builder().effectiveRate(effectiveRate.doubleValue()).build();
            }
            case Const.COMMIT_TYPE_START_QUALITY: {
                if (Objects.isNull(task)) return TaskPrecommitRes.builder().qualitySampleCount(0).build();

                int extractNumber = qualityService.extractAndCheck(taskId, req.getExtractRatio());
                taskService.updateQuality(taskId, commitType);
                return TaskPrecommitRes.builder().qualitySampleCount(extractNumber).build();
            }
            case Const.COMMIT_TYPE_QUALITY_SUBMIT: {
                // 校验是否有质检数据没有核实
                qualityService.qualitySubmitCheck(taskId);
            }
            case Const.COMMIT_TYPE_QUALITY_REWORK:
                if (Objects.isNull(task)) return emptyQualityRedo();
                return qualityRedo(commitType, task);
            case Const.COMMIT_TYPE_QUALITY_REPAIR: {
                if (Objects.isNull(task)) return emptyQualityRedo();
                qualityService.qualitySubmitCheck(taskId);
                return qualityRedo(commitType, task);
            }
            default: {
                return TaskPrecommitRes.builder().build();
            }
        }
    }

    private TaskPrecommitRes qualityRedo(Integer commitType, Task task) {
        String taskId = task.getTaskId();
        QualityStatisticDTO statistic = qualityService.statistic(taskId);
        TaskPrecommitRes.TaskPrecommitResBuilder builder = TaskPrecommitRes.builder()
                .qualityCorrectCount(statistic.getCorrectNumber())
                .qualityAccuracy(statistic.getAccuracyRatio().doubleValue());
        if (Objects.equals(Const.COMMIT_TYPE_QUALITY_SUBMIT, commitType))
            builder.effectiveRate(matLinkService.calculateEffectiveRate(taskId).doubleValue());
        if (Objects.equals(Const.COMMIT_TYPE_QUALITY_REWORK, commitType))
            builder.qualityRedoCount(statistic.getTotalNumber()).rebutTag(Const.REBUT_TAG_REWORK);
        if (Objects.equals(Const.COMMIT_TYPE_QUALITY_REPAIR, commitType))
            builder.qualityRepairCount(statistic.getErrorNumber()).rebutTag(Const.REBUT_TAG_REPAIR);

        builder.effectiveRate(0D);
        if (!isAttribute(task.getSubType())) {
            BigDecimal effectiveRate = matLinkService.calculateEffectiveRate(taskId);
            builder.effectiveRate(effectiveRate.doubleValue());
        }

        taskService.updateQuality(taskId, commitType);

        return builder.build();
    }

    private static TaskPrecommitRes emptyQualityRedo() {
        return TaskPrecommitRes.builder()
                .qualitySampleCount(0).qualityCorrectCount(0).qualityAccuracy(0.0D)
                .effectiveRate(0.0D).qualityRedoCount(0)
                .qualityRepairCount(0).build();
    }

    @DSTransactional
    public boolean create(TaskCreateReq req) {
        if (this.lambdaQuery().eq(Task::getTaskId, req.getTaskId()).count() > 0) return true;
        Task t = new Task();
        t.setTaskId(req.getTaskId());
        t.setCollectVersion(req.getCollectVersion());
        t.setTileId(req.getTileId());
        t.setType(req.getType());
        t.setSubType(req.getSubType());
        t.setDatasource(req.getDatasource());
        t.setDsId(req.getDsId());
        t.setMarketCode(req.getMarketCode());
        t.setMarketName(req.getMarketName());
        t.setCreateTime(LocalDateTime.now());
        t.setUpdateTime(t.getCreateTime());
        t.setQualityStatus(Const.TASK_QUALITY_STATUS_DEFAULT);
        t.setInspectStatus(Const.TASK_INSPECT_STATUS_DEFAULT);
        return this.save(t);
    }

    public TaskWorkInfoRes workInfo(String taskId) {
        Task task = this.taskService.lambdaQuery().eq(Task::getTaskId, taskId).one();
        if (Objects.isNull(task)) return TaskWorkInfoRes.builder().build();
        Integer subType = task.getSubType();
        if (isAttribute(subType)) {
            return dataminingService.workInfo(taskId);
        }
        return matLinkService.workInfo(taskId);
    }

    public void updateQuality(String taskId, Integer commitType) {
        this.lambdaUpdate().eq(Task::getTaskId, taskId)
                .set(Task::getQualityStatus, explainCommitType(commitType))
                .set(Task::getUpdateTime, LocalDateTime.now()).update();
    }

    private Integer explainCommitType(Integer commitType) {
        switch (commitType) {
            case Const.COMMIT_TYPE_START_QUALITY: {
                return Const.TASK_QUALITY_STATUS_STARTED;
            }
            case Const.COMMIT_TYPE_QUALITY_SUBMIT: {
                return Const.TASK_QUALITY_STATUS_SUBMIT;
            }
            case Const.COMMIT_TYPE_QUALITY_REPAIR: {
                return Const.TASK_QUALITY_STATUS_REPAIR;
            }
            case Const.COMMIT_TYPE_QUALITY_REWORK: {
                return Const.TASK_QUALITY_STATUS_REWORK;
            }
            default:
                throw new BizException("The specified commitType is wrong");
        }
    }

    // 是否属性任务
    public boolean isAttribute(Integer subType) {
        return Objects.equals(subType, Const.SUB_TYPE_LINK_ATTRIBUTE)
               || Objects.equals(subType, Const.SUB_TYPE_VS_ATTRIBUTE);
    }
}
