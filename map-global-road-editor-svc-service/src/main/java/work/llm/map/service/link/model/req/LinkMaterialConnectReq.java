package work.llm.map.service.link.model.req;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import work.llm.map.dao.link.entity.Link;
import work.llm.map.dao.matlink.entity.MatLink;
import work.llm.map.service.common.DatasourceAndTaskIdReq;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "道路资料挂接请求")
public class LinkMaterialConnectReq extends DatasourceAndTaskIdReq {

    @JsonAlias("meshLinkTemplate")
    @Schema(description = "道路资料信息")
    private MatLink matLink;
    @Schema(description = "被连接道路信息")
    private Link link;
    private Boolean auto = false;
}
