package work.llm.map.service.rule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.lalaframework.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import work.llm.map.common.constant.Const;
import work.llm.map.common.utils.OlvUtil;
import work.llm.map.common.utils.StatusUtil;
import work.llm.map.dao.common.entity.LinksNodeBase;
import work.llm.map.dao.link.entity.Link;
import work.llm.map.dao.rule.entity.Rule;
import work.llm.map.dao.rule.mapper.RuleMapper;
import work.llm.map.service.common.CommonService;
import work.llm.map.service.common.LinkCalculateReq;
import work.llm.map.service.common.PathCalculator;
import work.llm.map.service.link.LinkService;
import work.llm.map.service.node.NodeService;
import work.llm.map.service.rule.model.req.RuleAttributeReq;
import work.llm.map.service.rule.model.req.RuleDeleteReq;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class RuleService extends ServiceImpl<RuleMapper, Rule> {

    @Resource
    private LinkService linkService;
    @Resource
    private NodeService nodeService;
    @Resource
    private CommonService commonService;

    public Rule queryById(String ruleId, String status) {
        Rule rule = this.lambdaQuery().eq(Rule::getRuleId, ruleId).in(Rule::getStatus, StatusUtil.status(status)).one();
        if (Objects.nonNull(rule)) commonService.links(CollUtil.toList(rule));
        return rule;
    }

    public List<Rule> queryByExtent(String extent, String status) {
        Map<String, Link> linkMap = linkService.queryMapByExtent(extent);
        if (CollectionUtils.isEmpty(linkMap)) return null;

        // 收集hllLinkid
        List<String> hllLinkids = new ArrayList<>(linkMap.keySet());

        // 查询relation
        List<Rule> rules = this.lambdaQuery().in(Rule::getStatus, StatusUtil.status(status)).and(q -> q.in(Rule::getInlinkId, hllLinkids).or().in(Rule::getOutlinkId, hllLinkids)).list();

        // 补充links和node信息
        commonService.links(rules);

        return rules;
    }

    public Rule calculate(LinkCalculateReq req) {
        Link inlink = this.linkService.getById(req.getInlinkId());
        Link outlink = this.linkService.getById(req.getOutlinkId());
        String inlinkId = inlink.getHllLinkid();
        String outlinkId = outlink.getHllLinkid();

        List<String> pathLinkids;
        Map<String, String> joinNodeMap = null;
        Map<String, Link> linkMap = null;
        if (!inlinkId.equals(outlinkId)) {
            PathCalculator calculator = new PathCalculator(inlinkId, outlinkId);
            pathLinkids = calculator.ruleComplete();
            linkMap = calculator.getLinkMap();
        } else {
            pathLinkids = new ArrayList<>(Collections.singletonList(inlinkId));
        }

        Rule rule = new Rule();
        String ruleId = String.valueOf(commonService.generateId(inlink.getDatasource()));
        rule.setRuleId(ruleId);
        rule.setTileType(inlink.getTileType());
        rule.setTileId(inlink.getTileId());
        rule.setCp(inlink.getCp());
        rule.setTaskId(inlink.getTaskId());

        String pass;
        if (pathLinkids.size() == 1) {
            rule.setInlinkId(pathLinkids.get(0));
            rule.setOutlinkId(pathLinkids.get(0));
        } else if (pathLinkids.size() == 2) {
            rule.setInlinkId(pathLinkids.get(0));
            rule.setOutlinkId(pathLinkids.get(pathLinkids.size() - 1));
        } else {
            rule.setInlinkId(pathLinkids.get(0));
            rule.setOutlinkId(pathLinkids.get(pathLinkids.size() - 1));
            pass = StringUtils.join(pathLinkids.subList(1, pathLinkids.size() - 1), Const.SYMBOL_PIPE);
            rule.setPass(pass);
        }

        if (!inlinkId.equals(outlinkId)) {
            Link inLink = linkMap.get(inlinkId);
            Link inLinkNext = linkMap.get(pathLinkids.get(1));
            Link outLink = linkMap.get(outlinkId);
            Link outLinkBefore = linkMap.get(pathLinkids.get(pathLinkids.size() - 2));

            if (inLink.getHllSNid().equals(inLinkNext.getHllSNid()) ||  inLink.getHllSNid().equals(inLinkNext.getHllENid()) ) {
                rule.setNodeId(inLink.getHllSNid());
            } else {
                rule.setNodeId(inLink.getHllENid());
            }
            int ruleInfo = commonService.ruleInfo(inLink, outLink, inLinkNext, outLinkBefore);
            rule.setRuleInfo(ruleInfo);
        } else {
            rule.setNodeId(inlink.getHllENid());
            rule.setRuleInfo(Const.RULE_INFO_TURN);
        }

        commonService.links(CollUtil.toList(rule));
        return rule;
    }

    public Rule add(RuleAttributeReq req) {
        Rule rule = req.getRule();

        Map<String, Link> linkMap = this.linkService.check(rule.getInlinkId(), rule.getOutlinkId(), rule.getPass());
        this.nodeService.check(rule.getNodeId());

        Link inlink = linkMap.get(rule.getInlinkId());
        rule.setCp(inlink.getCp());
        rule.setTileType(inlink.getTileType());
        rule.setTileId(inlink.getTileId());
        rule.setUpDate(LocalDateTime.now());
        rule.setArea(inlink.getArea());

        rule.setRuleId(rule.getRuleId());
        rule.setStatus(Const.STATUS_NEW);
        rule.setTaskId(req.getTaskId());
        rule.setDatasource(req.getDatasource());
        this.save(rule);

        commonService.links(CollUtil.toList(rule));
        return rule;
    }

    public Rule update(RuleAttributeReq req) {
        Rule rule = req.getRule();

        Map<String, Link> linkMap = this.linkService.check(rule.getInlinkId(), rule.getOutlinkId(), rule.getPass());
        this.nodeService.check(rule.getNodeId());

        Link inlink = linkMap.get(rule.getInlinkId());
        rule.setCp(inlink.getCp());
        rule.setTileType(inlink.getTileType());
        rule.setTileId(inlink.getTileId());
        rule.setUpDate(LocalDateTime.now());
        rule.setArea(inlink.getArea());

        rule.setStatus(Const.STATUS_UPDATED);
        rule.setTaskId(req.getTaskId());
        rule.setDatasource(commonService.datasource(req.getDatasource(), rule.getDatasource()));
        boolean updated = this.updateById(rule);
        OlvUtil.check(updated, Const.RULE);

        commonService.links(CollUtil.toList(rule));
        return rule;
    }

    @DSTransactional
    public boolean update(RuleDeleteReq req) {
        LocalDateTime now = LocalDateTime.now();
        boolean updated;
        List<Rule> rules = req.getRules();
        for (Rule rule : rules) {
            rule.setUpDate(now);
            rule.setTaskId(req.getTaskId());
            rule.setDatasource(commonService.datasource(req.getDatasource(), rule.getDatasource()));
            rule.setStatus(Const.STATUS_DELETED);
            updated = this.updateById(rule);
            OlvUtil.check(updated, Const.RULE);
        }
        return true;
    }

    /**
     * 打断线路对Rule的操作
     *
     * @param hllLinkid 打断线路的meshLink
     * @param link1     打断后的线路1
     * @param link2     打断后的线路2
     */
    @DSTransactional
    public void split(String hllLinkid, Link link1, Link link2) {
        log.info("进入打断rule逻辑，参数hllLinkid=[{}], taskId=[{}]", hllLinkid, link1.getTaskId());
        List<Rule> rules = this.lambdaQuery().ne(Rule::getStatus, Const.STATUS_DELETED)
                .and(q -> q.eq(Rule::getInlinkId, hllLinkid).or().eq(Rule::getOutlinkId, hllLinkid).or().like(Rule::getPass, hllLinkid)).list();

        if (CollectionUtils.isEmpty(rules)) return;

        log.info("需要打断[{}]条rule数据:{}", rules.size(), Arrays.toString(rules.toArray()));

        updateLink(hllLinkid, link1, link2, rules);

        log.info("处理rule逻辑结束，参数hllLinkid=[{}], taskId=[{}]", hllLinkid, link1.getTaskId());
    }

    /**
     * 更新link信息
     *
     * @param hllLinkid 打断的线路
     * @param link1     打断后的线路第一段
     * @param link2     打断后的线路第二段
     * @param rules     数据集合
     */
    @DSTransactional
    public void updateLink(String hllLinkid, Link link1, Link link2, List<Rule> rules) {
        String datasource = link1.getDatasource();
        String taskId = link1.getTaskId();
        LocalDateTime upDate = link1.getUpDate();

        QueryWrapper<Link> tQuery = new QueryWrapper<>();
        tQuery.eq("hll_linkid", String.valueOf(hllLinkid));
        Link originLink = this.linkService.getBaseMapper().selectOne(tQuery);
        String s = originLink.getHllSNid();
        String e = originLink.getHllENid();
        List<Rule> flag = new ArrayList<>();

        if (rules.size() >= 2) {
            for (Rule t : rules) {
                if (t.getInlinkId().equals(t.getOutlinkId()) && (t.getNodeId().equals(s) || t.getNodeId().equals(e))) {
                    flag.add(t);
                }
            }
        }
        // 处理数据
        Map<String, Link> linkMap;
        List<String> needToCheckLinkids = new ArrayList<>();
        boolean updated;
        for (Rule rule : rules) {
            // 进入线和退出线一致，肯定没有pass
            if (rule.getInlinkId().equals(rule.getOutlinkId())) {
                rule.setTaskId(taskId);
                rule.setDatasource(commonService.datasource(datasource, rule.getDatasource()));
                rule.setStatus(Const.STATUS_UPDATED);
                rule.setUpDate(upDate);
                if (link1.getHllSNid().equals(rule.getNodeId()) || link1.getHllENid().equals(rule.getNodeId())) {
                    rule.setInlinkId(link1.getHllLinkid());
                    rule.setOutlinkId(link1.getHllLinkid());
                    updated = this.updateById(rule);
                    OlvUtil.check(updated, Const.RULE);
                    if (flag.size() == 2) {
                        Rule newRule = new Rule();
                        BeanUtils.copyProperties(rule, newRule);
                        Long id = commonService.generateId(datasource);
                        if (link1.getHllSNid().equals(rule.getNodeId())) {
                            newRule.setNodeId(link1.getHllENid());
                        } else {
                            newRule.setNodeId(link1.getHllSNid());
                        }
                        newRule.setRuleId(String.valueOf(id));
                        newRule.setStatus(Const.STATUS_NEW);
                        newRule.setUpDate(upDate);
                        this.save(newRule);
                    }
                } else {
                    rule.setInlinkId(link2.getHllLinkid());
                    rule.setOutlinkId(link2.getHllLinkid());
                    updated = this.updateById(rule);
                    OlvUtil.check(updated, Const.RULE);
                    if (flag.size() == 2) {
                        Rule newRule = new Rule();
                        Long id = commonService.generateId(datasource);
                        if (link2.getHllSNid().equals(rule.getNodeId())) {
                            newRule.setNodeId(link2.getHllENid());
                        } else {
                            newRule.setNodeId(link2.getHllSNid());
                        }
                        newRule.setRuleId(String.valueOf(id));
                        newRule.setStatus(Const.STATUS_NEW);
                        newRule.setUpDate(upDate);
                        this.save(newRule);
                    }
                }
                continue;
            }

            // 进入线与退出线不一致
            if (rule.getInlinkId().equals(hllLinkid)) {
                String afterLinkid;
                // 打断的是inlink，检查pass和outlink
                if (Objects.nonNull(rule.getOutlinkId())) needToCheckLinkids.add(rule.getOutlinkId());
                if (StringUtils.isNotBlank(rule.getPass())) {
                    String[] passes = rule.getPass().split("\\|");
                    afterLinkid = passes[0];
                    needToCheckLinkids.addAll(Arrays.asList(passes));
                } else {
                    afterLinkid = rule.getOutlinkId();
                }
                linkMap = this.linkService.check(needToCheckLinkids, Const.RULE, rule.getRuleId());

                Link outLink = linkMap.get(afterLinkid);
                if (link1.getHllSNid().equals(outLink.getHllSNid()) || link1.getHllSNid().equals(outLink.getHllENid()) || link1.getHllENid().equals(outLink.getHllSNid()) || link1.getHllENid().equals(outLink.getHllENid())) {
                    rule.setInlinkId(link1.getHllLinkid());
                } else {
                    rule.setInlinkId(link2.getHllLinkid());
                }
            } else if (Objects.nonNull(rule.getOutlinkId()) && rule.getOutlinkId().equals(hllLinkid)) {
                String beforeLinkid;
                // 打断的是outlink，检查pass和inlink
                needToCheckLinkids.add(rule.getInlinkId());
                if (StringUtils.isNotBlank(rule.getPass())) {
                    String[] passes = rule.getPass().split("\\|");
                    beforeLinkid = passes[passes.length - 1];
                    needToCheckLinkids.addAll(Arrays.asList(passes));
                } else {
                    beforeLinkid = rule.getInlinkId();
                }
                linkMap = this.linkService.check(needToCheckLinkids, Const.RULE, rule.getRuleId());

                Link inLink = linkMap.get(beforeLinkid);
                if (link1.getHllSNid().equals(inLink.getHllSNid()) || link1.getHllSNid().equals(inLink.getHllENid()) || link1.getHllENid().equals(inLink.getHllSNid()) || link1.getHllENid().equals(inLink.getHllENid())) {
                    rule.setOutlinkId(link1.getHllLinkid());
                } else {
                    rule.setOutlinkId(link2.getHllLinkid());
                }
            } else {
                // 拼接pass
                List<String> passLinkIds = Arrays.stream(rule.getPass().split("\\|")).collect(Collectors.toList());
                int hllLinkidIdx = passLinkIds.indexOf(String.valueOf(hllLinkid));

                // 打断的是pass，检查inlink和pass非打断和outlink
                if (Objects.nonNull(rule.getInlinkId())) needToCheckLinkids.add(rule.getInlinkId());
                if (Objects.nonNull(rule.getOutlinkId())) needToCheckLinkids.add(rule.getOutlinkId());
                for (int i = 0; i < passLinkIds.size(); i++) {
                    if (i == hllLinkidIdx) continue;
                    needToCheckLinkids.add(passLinkIds.get(i));
                }

                linkMap = this.linkService.check(needToCheckLinkids, Const.RULE, rule.getRuleId());

                String beforeLinkid;
                if (passLinkIds.size() == 1 || hllLinkidIdx == 0) {
                    beforeLinkid = rule.getInlinkId();
                } else {
                    beforeLinkid = passLinkIds.get(hllLinkidIdx - 1);
                }
                Link beforeLink = linkMap.get(beforeLinkid);

                if (beforeLink.getHllSNid().equals(link1.getHllSNid()) || beforeLink.getHllSNid().equals(link1.getHllENid()) || beforeLink.getHllENid().equals(link1.getHllSNid()) | beforeLink.getHllENid().equals(link1.getHllENid())) {
                    passLinkIds.set(hllLinkidIdx, String.valueOf(link1.getHllLinkid()));
                    passLinkIds.add(++hllLinkidIdx, String.valueOf(link2.getHllLinkid()));
                } else {
                    passLinkIds.set(hllLinkidIdx, String.valueOf(link2.getHllLinkid()));
                    passLinkIds.add(++hllLinkidIdx, String.valueOf(link1.getHllLinkid()));
                }

                // 组装pass
                String pass = String.join("|", passLinkIds);
                rule.setPass(pass);
            }
            // 更新rule
            rule.setStatus(Const.STATUS_UPDATED);
            rule.setDatasource(commonService.datasource(datasource, rule.getDatasource()));
            rule.setUpDate(upDate);
            rule.setTaskId(taskId);
            updated = this.updateById(rule);
            OlvUtil.check(updated, Const.RULE);
        }
    }

    @DSTransactional
    public void update(String hllLinkid, String taskId, String datasource, Integer status) {
        log.info("关联更新rule逻辑，参数hllLinkid=[{}], taskId=[{}]", hllLinkid, taskId);
        List<Rule> rules = this.lambdaQuery().ne(Rule::getStatus, Const.STATUS_DELETED)
                .and(q -> q.eq(Rule::getInlinkId, hllLinkid).or()
                        .like(Rule::getPass, hllLinkid).or().eq(Rule::getOutlinkId, hllLinkid)).list();
        if (CollUtil.isEmpty(rules)) return;

        log.info("关联更新[{}]条rule数据:{}", rules.size(), Arrays.toString(rules.toArray()));

        LocalDateTime now = LocalDateTime.now();
        boolean updated;
        for (Rule rule : rules) {
            rule.setStatus(status);
            if (status == Const.STATUS_DELETED) {
                if (StrUtil.isNotEmpty(rule.getPass()) && rule.getPass().contains(String.valueOf(hllLinkid))) {
                    rule.setStatus(Const.STATUS_UPDATED);
                }
            }
            rule.setUpDate(now);
            rule.setDatasource(commonService.datasource(datasource, rule.getDatasource()));
            rule.setTaskId(taskId);
            updated = this.updateById(rule);
            OlvUtil.check(updated, Const.RULE);
        }
    }

    @DSTransactional
    public void update4ConnectNode(String hllNodeid, String taskId, String datasource) {
        List<Rule> rules = this.lambdaQuery().eq(LinksNodeBase::getNodeId, hllNodeid)
                .ne(Rule::getStatus, Const.STATUS_DELETED)
                .list();

        if (CollUtil.isEmpty(rules)) return;

        LocalDateTime now = LocalDateTime.now();
        for (Rule rule : rules) {
            rule.setUpDate(now);
            rule.setStatus(Const.STATUS_UPDATED);
            rule.setTaskId(taskId);
            rule.setDatasource(commonService.datasource(datasource, rule.getDatasource()));
            this.updateById(rule);
        }
    }
}
