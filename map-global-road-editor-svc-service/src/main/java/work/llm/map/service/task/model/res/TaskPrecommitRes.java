package work.llm.map.service.task.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import work.llm.map.common.constant.Const;

@Data
@ToString
@Schema(description = "任务预提交响应类")
@Builder
public class TaskPrecommitRes {

    @Builder.Default
    @Schema(description = "返工返修标记: 0:默认正常,不驳回, 1:返修, 2:返工")
    private Integer rebutTag = Const.REBUT_TAG_DEFAULT;

    @Schema(description = "质检抽取数量")
    private Integer qualitySampleCount;

    @Schema(description = "验收抽取数量")
    private Integer verifySampleCount;

    @Schema(description = "质检返修数量")
    private Integer qualityRepairCount;

    @Schema(description = "质检返工数量")
    private Integer qualityRedoCount;

    @Schema(description = "质检正确率")
    private Double qualityAccuracy;

    @Schema(description = "验收正确率")
    private Double verifyAccuracy;

    @Schema(description = "质检正确数量")
    private Integer qualityCorrectCount;

    @Schema(description = "验收正确数量")
    private Integer verifyCorrectCount;

    @Schema(description = "作业有效率")
    private Double effectiveRate;
}
