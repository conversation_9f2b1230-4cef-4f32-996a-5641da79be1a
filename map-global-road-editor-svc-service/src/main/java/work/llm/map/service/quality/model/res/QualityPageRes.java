package work.llm.map.service.quality.model.res;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import work.llm.map.dao.quality.entity.Quality;

@Data
@Schema(description = "质检列表分页返回类")
public class QualityPageRes {

    @Schema(description = "质检信息")
    private Page<Quality> qualityPage;
    @Schema(description = "质检个数")
    private Integer qualityNumber;
}
