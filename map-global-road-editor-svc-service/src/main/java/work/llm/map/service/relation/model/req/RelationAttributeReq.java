package work.llm.map.service.relation.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import work.llm.map.dao.relation.entity.Relation;
import work.llm.map.service.common.DatasourceAndTaskIdReq;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "道路关系属性请求")
public class RelationAttributeReq extends DatasourceAndTaskIdReq {

    @Schema(description = "关系属性")
    private Relation relation;
}
