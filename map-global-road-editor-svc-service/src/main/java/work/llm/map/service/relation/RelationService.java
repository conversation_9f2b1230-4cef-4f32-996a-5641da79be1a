package work.llm.map.service.relation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.lalaframework.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import work.llm.map.common.constant.Const;
import work.llm.map.common.exception.BizException;
import work.llm.map.common.utils.OlvUtil;
import work.llm.map.common.utils.StatusUtil;
import work.llm.map.dao.common.entity.LinksNodeBase;
import work.llm.map.dao.link.entity.Link;
import work.llm.map.dao.node.entity.Node;
import work.llm.map.dao.relation.entity.Relation;
import work.llm.map.dao.relation.mapper.RelationMapper;
import work.llm.map.service.common.CommonService;
import work.llm.map.service.link.LinkService;
import work.llm.map.service.node.NodeService;
import work.llm.map.service.relation.model.req.RelationAttributeReq;
import work.llm.map.service.relation.model.req.RelationDeleteReq;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class RelationService extends ServiceImpl<RelationMapper, Relation> {

    @Resource
    private LinkService linkService;
    @Resource
    private NodeService nodeService;
    @Resource
    private CommonService commonService;

    @DSTransactional
    public Relation queryById(String relationId, String status) {
        Relation relation = this.lambdaQuery().eq(Relation::getRelationId, relationId)
                .in(Relation::getStatus, StatusUtil.status(status)).one();
        if (Objects.nonNull(relation)) commonService.links(CollUtil.toList(relation));
        return relation;
    }

    public List<Relation> queryByExtent(String extent, String status) {
        Map<String, Link> linkMap = linkService.queryMapByExtent(extent);
        if (CollectionUtils.isEmpty(linkMap)) return null;

        // 收集hllLinkid
        List<String> hllLinkids = new ArrayList<>(linkMap.keySet());

        // 查询relation
        List<Relation> relations = this.lambdaQuery()
                .in(Relation::getStatus, StatusUtil.status(status))
                .and(q -> q.in(Relation::getInlinkId, hllLinkids).or().in(Relation::getOutlinkId, hllLinkids)).list();

        // 补充links和node信息
        commonService.links(relations);

        return relations;
    }

    @DSTransactional
    public Relation add(RelationAttributeReq req) {
        Relation relation = req.getRelation();

        checkAndSetConnection(relation);
        checkAndSetTile(relation);

        Node node = nodeService.lambdaQuery().select(Node::getArea, Node::getHllNodeid)
                .eq(Node::getHllNodeid, relation.getNodeId()).ne(Node::getStatus, Const.STATUS_DELETED).one();
        relation.setArea(node.getArea());
        relation.setRelationId(String.valueOf(commonService.generateId(req.getDatasource())));
        relation.setUpDate(LocalDateTime.now());
        relation.setTaskId(req.getTaskId());
        relation.setDatasource(commonService.datasource(req.getDatasource(), relation.getDatasource()));
        relation.setStatus(Const.STATUS_NEW);
        this.save(relation);

        return this.queryById(relation.getRelationId(), Const.DEFAULT_QUERY_STATUS);
    }

    @DSTransactional
    public Relation update(RelationAttributeReq req) {
        Relation relation = req.getRelation();

        checkAndSetConnection(relation);
        checkAndSetTile(relation);

        Node node = nodeService.lambdaQuery().select(Node::getArea, Node::getHllNodeid)
                .eq(Node::getHllNodeid, relation.getNodeId()).ne(Node::getStatus, Const.STATUS_DELETED).one();
        relation.setArea(node.getArea());
        relation.setUpDate(LocalDateTime.now());
        relation.setTaskId(req.getTaskId());
        relation.setDatasource(commonService.datasource(req.getDatasource(), relation.getDatasource()));
        relation.setStatus(Const.STATUS_UPDATED);
        boolean updated = this.updateById(relation);
        OlvUtil.check(updated, Const.RELATION);

        return this.queryById(relation.getRelationId(), Const.DEFAULT_QUERY_STATUS);
    }

    private void checkAndSetTile(Relation relation) {
        this.linkService.check(relation.getInlinkId(), relation.getOutlinkId(), null);
        Node node = this.nodeService.check(relation.getNodeId());

        relation.setTileType(node.getTileType());
        relation.setTileId(node.getTileId());
    }

    private void checkAndSetConnection(Relation relation) {
        if (relation.getType().equals(Const.RELATION_TYPE_TRAFFIC_LIGHTS)) {
            relation.setOutlinkId(null);
        }
        List<String> hllLinkids = new ArrayList<>();
        if (StrUtil.isNotBlank(relation.getInlinkId())) hllLinkids.add(relation.getInlinkId());
        if (StrUtil.isNotBlank(relation.getOutlinkId())) hllLinkids.add(relation.getOutlinkId());
        if (CollectionUtils.isEmpty(hllLinkids))
            throw new BizException("The inlinkId and outlinkId can not both be empty.");
        Map<String, Link> linkMap = this.linkService.queryMapByLinkids(hllLinkids);
        String nodeId = relation.getNodeId();
        for (Link link : linkMap.values()) {
            if (!ObjUtil.equals(link.getHllSNid(), nodeId)
                && !ObjUtil.equals(link.getHllENid(), nodeId)) {
                throw new BizException("The inlinkId or outlinkId has not connection with the nodeId.");
            }
        }
    }

    @DSTransactional
    public boolean update(RelationDeleteReq req) {
        LocalDateTime now = LocalDateTime.now();
        boolean updated;
        List<Relation> relations = req.getRelations();
        for (Relation relation : relations) {
            relation.setUpDate(now);
            relation.setTaskId(req.getTaskId());
            relation.setDatasource(commonService.datasource(req.getDatasource(), relation.getDatasource()));
            relation.setStatus(Const.STATUS_DELETED);
            updated = this.updateById(relation);
            OlvUtil.check(updated, Const.RELATION);
        }
        return true;
    }

    /**
     * 打断线路对Relation的操作
     *
     * @param hllLinkid 打断线路的linkId
     * @param link1     打断后的线路1
     * @param link2     打断后的线路2
     */
    @DSTransactional
    public void split(String hllLinkid, Link link1, Link link2) {
        String taskId = link1.getTaskId();
        String datasource = link1.getDatasource();
        LocalDateTime upDate = link1.getUpDate();

        log.info("进入打断relation逻辑,参数=>hllLinkid=[{}],taskId=[{}]", hllLinkid, taskId);
        LocalDateTime now = LocalDateTime.now();
        // 查询Relation中当前collectVersion的数据是否存在，如果不存在再查询Relation数据
        List<Relation> relations = this.lambdaQuery().ne(Relation::getStatus, Const.STATUS_DELETED)
                .and(q -> q.eq(Relation::getInlinkId, hllLinkid)
                        .or().eq(Relation::getOutlinkId, hllLinkid)).list();
        if (CollectionUtils.isEmpty(relations)) return;

        log.info("需要打断[{}]条relation数据:{}", relations.size(), Arrays.toString(relations.toArray()));

        boolean updated;
        for (Relation relation : relations) {

            // 增加判断，如果inlink和outlink相同，抛出异常
            if (relation.getInlinkId() != null && relation.getOutlinkId() != null && relation.getInlinkId().equals(relation.getOutlinkId())) {
                throw new BizException("There is invalid data in the Relation data for hllLinkid [" + hllLinkid + "]. The inlink and outlink are identical.");
            }

            relation.setUpDate(now);
            QueryWrapper<Link> queryWrapper = new QueryWrapper<>();
            queryWrapper.ne("status", Const.STATUS_DELETED);
            if (relation.getInlinkId() != null && relation.getInlinkId().equals(hllLinkid)) {
                /*
                  区分两种情况：
                   ① outlink不为空，判断outlink的起点和终点是不是与nodeId相等
                   ② outlink为空，判断inlink起点终点是不是与nodeId相等
                       a.相等通过
                       b.不相等，查询以此点为主点的点是不是包含nodeId
                 */
                String outlinkId = relation.getOutlinkId();
                if (outlinkId == null) {
                    singleLink(link1, link2, relation, false);
                } else {
                    queryWrapper.eq("hll_linkid", outlinkId);
                    Link outLink = this.linkService.getBaseMapper().selectOne(queryWrapper);
                    if (link1.getHllSNid().equals(outLink.getHllSNid()) || link1.getHllSNid().equals(outLink.getHllENid()) || link1.getHllENid().equals(outLink.getHllSNid()) || link1.getHllENid().equals(outLink.getHllENid())) {
                        relation.setInlinkId(link1.getHllLinkid());
                    } else {
                        relation.setInlinkId(link2.getHllLinkid());
                    }
                }
            } else if (relation.getOutlinkId() != null && relation.getOutlinkId().equals(hllLinkid)) {
                String inlinkId = relation.getInlinkId();
                if (inlinkId == null) {
                    singleLink(link1, link2, relation, true);
                } else {
                    queryWrapper.eq("hll_linkid", inlinkId);
                    Link inLink = this.linkService.getBaseMapper().selectOne(queryWrapper);
                    if (link1.getHllSNid().equals(inLink.getHllSNid()) || link1.getHllSNid().equals(inLink.getHllENid()) || link1.getHllENid().equals(inLink.getHllSNid()) || link1.getHllENid().equals(inLink.getHllENid())) {
                        relation.setOutlinkId(link1.getHllLinkid());
                    } else {
                        relation.setOutlinkId(link2.getHllLinkid());
                    }
                }
            }

            relation.setTaskId(taskId);
            relation.setStatus(Const.STATUS_UPDATED);
            relation.setDatasource(commonService.datasource(datasource, relation.getDatasource()));
            relation.setUpDate(upDate);
            updated = this.updateById(relation);
            OlvUtil.check(updated, Const.RELATION);
        }

        log.info("处理relation逻辑结束,参数=>hllLinkid=[{}],taskId=[{}]", hllLinkid, taskId);
    }

    /**
     * inlink或者outlink为空
     *
     * @param link1    打断后的第一条
     * @param link2    打断后的第二条
     * @param relation relation
     */
    private void singleLink(Link link1, Link link2, Relation relation, boolean isInlinkNull) {
        String nodeId = relation.getNodeId();
        if (nodeId.equals(link1.getHllSNid()) || nodeId.equals(link1.getHllENid())) {
            if (isInlinkNull) {
                relation.setOutlinkId(link1.getHllLinkid());
            } else {
                relation.setInlinkId(link1.getHllLinkid());
            }
            return;
        } else if (nodeId.equals(link2.getHllSNid()) || nodeId.equals(link2.getHllENid())) {
            if (isInlinkNull) {
                relation.setOutlinkId(link2.getHllLinkid());
            } else {
                relation.setInlinkId(link2.getHllLinkid());
            }
            return;
        }
        Node node = nodeService.lambdaQuery().ne(Node::getStatus, Const.STATUS_DELETED)
                .eq(Node::getHllNodeid, nodeId).one();

        String mainnodeid = node.getMainnodeid();
        if (StrUtil.isEmpty(mainnodeid)) {
            throw new BizException("There is invalid data in the Relation data for relationId [" + relation.getRelationId() + "]. The start and end points of the inlink do not have a valid connection with the nodeId.");
        }

        List<Node> subNodes = this.nodeService.lambdaQuery().ne(Node::getStatus, Const.STATUS_DELETED)
                .eq(Node::getMainnodeid, mainnodeid).list();
        boolean connected = false;
        for (Node subNode : subNodes) {
            String hllNodeid = subNode.getHllNodeid();
            if (hllNodeid.equals(link1.getHllSNid()) || hllNodeid.equals(link1.getHllENid())) {
                if (isInlinkNull) {
                    relation.setOutlinkId(link1.getHllLinkid());
                } else {
                    relation.setInlinkId(link1.getHllLinkid());
                }
                connected = true;
                break;
            } else if (hllNodeid.equals(link2.getHllSNid()) || hllNodeid.equals(link2.getHllENid())) {
                if (isInlinkNull) {
                    relation.setOutlinkId(link2.getHllLinkid());
                } else {
                    relation.setInlinkId(link2.getHllLinkid());
                }
                connected = true;
                break;
            }
        }

        if (!connected) {
            throw new BizException("There is invalid data in the Relation data for relationId [" + relation.getRelationId() + "]. The start and end points of the inlink do not have a valid connection with the nodeId.");
        }
    }

    @DSTransactional
    public void update(String hllLinkid, String taskId, String datasource, Integer status) {
        log.info("关联更新relation逻辑，参数hllLinkid=[{}], taskId=[{}]", hllLinkid, taskId);
        List<Relation> relations = this.lambdaQuery().ne(Relation::getStatus, Const.STATUS_DELETED)
                .and(q -> q.eq(Relation::getInlinkId, hllLinkid)
                        .or().eq(Relation::getOutlinkId, hllLinkid)).list();
        if (CollUtil.isEmpty(relations)) return;

        log.info("关联更新[{}]条relation数据:{}", relations.size(), Arrays.toString(relations.toArray()));

        LocalDateTime now = LocalDateTime.now();
        boolean updated;
        for (Relation relation : relations) {
            relation.setStatus(status);
            relation.setUpDate(now);
            relation.setDatasource(commonService.datasource(datasource, relation.getDatasource()));
            relation.setTaskId(taskId);
            updated = this.updateById(relation);
            OlvUtil.check(updated, Const.RELATION);
        }
    }

    @DSTransactional
    public void update4ConnectNode(String hllNodeid, String taskId, String datasource) {
        List<Relation> relations = this.lambdaQuery().eq(LinksNodeBase::getNodeId, hllNodeid)
                .ne(Relation::getStatus, Const.STATUS_DELETED)
                .list();

        if (CollUtil.isEmpty(relations)) return;

        LocalDateTime now = LocalDateTime.now();
        for (Relation relation : relations) {
            relation.setUpDate(now);
            relation.setStatus(Const.STATUS_UPDATED);
            relation.setTaskId(taskId);
            relation.setDatasource(commonService.datasource(datasource, relation.getDatasource()));
            this.updateById(relation);
        }
    }
}
