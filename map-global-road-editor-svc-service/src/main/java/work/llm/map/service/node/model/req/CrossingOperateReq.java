package work.llm.map.service.node.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import work.llm.map.dao.node.entity.Node;
import work.llm.map.service.common.DatasourceAndTaskIdReq;

import java.util.ArrayList;
import java.util.List;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "路口操作请求类")
public class CrossingOperateReq extends DatasourceAndTaskIdReq {

    @Schema(description = "主点信息")
    private Node mainNode;
    @Schema(description = "子点信息集合")
    private List<Node> subNodes = new ArrayList<>();
}
