package work.llm.map.service.link;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import work.llm.map.dao.link.entity.LinkDiff;
import work.llm.map.dao.link.mapper.LinkDiffMapper;

import java.util.List;

@Slf4j
@Service
public class LinkDiffService extends ServiceImpl<LinkDiffMapper, LinkDiff> {

    public List<LinkDiff> queryByExtent(String extent) {
        return this.lambdaQuery()
                .apply("ST_Intersects(ST_GeomFromText({0}, 4326), geometry)", extent).list();
    }
}
