package work.llm.map.service.matlink.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import work.llm.map.common.constant.Const;

import java.util.List;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "资料状态更新请求")
public class MatLinkStatusReq {

    @Schema(description = "资料ID")
    private List<Long> matIds;
    @Schema(description = "1-已作业 2-已删除（目前只用于删除）")
    private Integer workStatus = Const.WORK_STATUS_DELETED;
}
