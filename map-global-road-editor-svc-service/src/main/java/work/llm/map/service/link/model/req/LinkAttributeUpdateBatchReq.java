package work.llm.map.service.link.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import work.llm.map.service.common.DatasourceAndTaskIdReq;

import java.util.List;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "道路属性批量更新请求")
public class LinkAttributeUpdateBatchReq extends DatasourceAndTaskIdReq {

    @Schema(description = "道路属性")
    private List<LinkReq> links;

    @Data
    public static class LinkReq {
        private String hllLinkid;
        private String kind;
        private String formway;
        private String dir;
        private String funct;
        private String app;
        private String toll;
        private String datasource;
        private String arVeh;
        private String nameChO;
        private Integer olv;
    }
}
