package work.llm.map.service.scheduler;

import cn.hutool.core.util.StrUtil;
import cn.lalaframework.dynamic.datasource.annotation.DSTransactional;
import cn.lalaframework.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import cn.lalaframework.job.sdk.HllXxlJobManager;
import cn.lalaframework.job.sdk.annotation.HllXxlJob;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vividsolutions.jts.geom.Geometry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import work.llm.map.common.constant.Const;
import work.llm.map.common.utils.JsonUtils;
import work.llm.map.dao.link.entity.Link;
import work.llm.map.dao.node.entity.Node;
import work.llm.map.dao.relation.entity.Relation;
import work.llm.map.dao.rule.entity.Rule;
import work.llm.map.service.common.CommonService;
import work.llm.map.service.link.LinkService;
import work.llm.map.service.node.NodeService;
import work.llm.map.service.relation.RelationService;
import work.llm.map.service.rule.RuleService;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@SuppressWarnings({"unchecked", "Duplicates"})
public class TileJobHandler {

    @Resource
    private LinkService linkService;
    @Resource
    private NodeService nodeService;
    @Resource
    private RuleService ruleService;
    @Resource
    private RelationService relationService;
    @Resource
    private CommonService commonService;

    @HllXxlJob(value = "tileJobHandler")
    @DSTransactional
    @SuppressWarnings("unchecked")
    public void execute() throws Exception {
        HllXxlJobManager.log("XXL-JOB, starting tileJobHandler...");
        // 获取执行参数
        String param = HllXxlJobManager.getJobParam();
        if (StrUtil.isEmpty(param)) return;

        Map<String, String> parameters = JsonUtils.parseJson(param, Map.class);
        HllXxlJobManager.log("tileJobHandler's parameters: " + parameters);

        String[] schemas = parameters.get("schemas").split(Const.SYMBOL_COMMA);
        List<String> tables = Arrays.stream(parameters.get("tables").split(Const.SYMBOL_COMMA)).collect(Collectors.toList());

        for (String schema : schemas) {
            String old = DynamicDataSourceContextHolder.peek();
            DynamicDataSourceContextHolder.push(schema);
            try {
                if (tables.contains("link")) doLink();
                if (tables.contains("node")) doNode();
                if (tables.contains("rule")) doRule();
                if (tables.contains("relation")) doRelation();
            } finally {
                DynamicDataSourceContextHolder.push(old);
            }
        }
    }

    public void doRelation() {
        try {
            while (true) {
                Page<Relation> page = relationService.lambdaQuery()
                        .isNull(Relation::getTileId)
                        .orderByAsc(Relation::getRelationId)
                        .page(new Page<>(1, 5000));
                if (page.getRecords().isEmpty()) return;
                Map<String, Geometry> geomMap = geometryMap(page.getRecords().stream().map(Relation::getNodeId).distinct().collect(Collectors.toList()));
                if (geomMap == null || geomMap.isEmpty()) break;
                List<Relation> peekedRelations = page.getRecords().stream().peek(relation -> {
                    relation.setTileType(1);
                    relation.setTileId(commonService.getTileId(geomMap.get(relation.getNodeId()).toText()));
                }).collect(Collectors.toList());
                relationService.updateBatchById(peekedRelations);
            }
        } catch (Exception e) {
            log.error("do refresh tile in table relation error", e);
        }
    }

    public Map<String, Geometry> geometryMap(List<String> nodeids) {
        List<Node> nodes = this.nodeService.getBaseMapper().selectBatchIds(nodeids);
        return nodes.stream().collect(Collectors.toMap(Node::getHllNodeid, Node::getGeometry));
    }


    public void doRule() {
        try {
            while (true) {
                Page<Rule> page = ruleService.lambdaQuery()
                        .isNull(Rule::getTileId)
                        .orderByAsc(Rule::getRuleId)
                        .page(new Page<>(1, 5000));
                if (page.getRecords().isEmpty()) return;
                Map<String, Geometry> geomMap = geometryMap(page.getRecords().stream().map(Rule::getNodeId).distinct().collect(Collectors.toList()));
                if (geomMap == null || geomMap.isEmpty()) break;
                List<Rule> peekedRules = page.getRecords().stream().peek(rule -> {
                    rule.setTileType(1);
                    rule.setTileId(commonService.getTileId(geomMap.get(rule.getNodeId()).toText()));
                }).collect(Collectors.toList());
                ruleService.updateBatchById(peekedRules);
            }
        } catch (Exception e) {
            log.error("do refresh tile in table rule error", e);
        }
    }

    public void doNode() {
        try {
            while (true) {
                Page<Node> page = nodeService.lambdaQuery()
                        .isNull(Node::getTileId)
                        .orderByAsc(Node::getHllNodeid)
                        .page(new Page<>(1, 5000));
                if (page.getRecords().isEmpty()) break;
                List<Node> peekedNodes = page.getRecords().stream().peek(node -> {
                    node.setTileType(1);
                    node.setTileId(commonService.getTileId(node.getGeometry().toText()));
                }).collect(Collectors.toList());
                nodeService.updateBatchById(peekedNodes);
            }
        } catch (Exception e) {
            log.error("do refresh tile in table node error", e);
        }
    }

    public void doLink() {
        try {
            while (true) {
                Page<Link> page = linkService.lambdaQuery()
                        .isNull(Link::getTileId)
                        .orderByAsc(Link::getHllLinkid)
                        .page(new Page<>(1, 5000));
                if (page.getRecords().isEmpty()) break;
                List<Link> peekedLinks = page.getRecords().stream().peek(link -> {
                    link.setTileType(1);
                    link.setTileId(commonService.getTileId(link.getGeometry().toText()));
                }).collect(Collectors.toList());
                linkService.updateBatchById(peekedLinks);
            }
        } catch (Exception e) {
            log.error("do refresh tile in table link error", e);
        }

    }
}
