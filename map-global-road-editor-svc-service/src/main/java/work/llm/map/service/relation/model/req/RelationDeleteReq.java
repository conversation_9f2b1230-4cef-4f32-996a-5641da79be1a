package work.llm.map.service.relation.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import work.llm.map.dao.relation.entity.Relation;
import work.llm.map.service.common.DatasourceAndTaskIdReq;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "道路关系删除请求")
public class RelationDeleteReq extends DatasourceAndTaskIdReq {

    @Schema(description = "待删除的道路关系")
    private List<Relation> relations;
}