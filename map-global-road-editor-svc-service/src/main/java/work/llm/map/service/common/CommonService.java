package work.llm.map.service.common;

import com.vividsolutions.jts.geom.Coordinate;
import com.vividsolutions.jts.geom.Geometry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import work.llm.map.common.constant.Const;
import work.llm.map.common.exception.BizException;
import work.llm.map.dao.common.entity.LinkVo;
import work.llm.map.dao.common.entity.LinksNodeBase;
import work.llm.map.dao.common.entity.NodeVo;
import work.llm.map.dao.link.entity.Link;
import work.llm.map.dao.node.entity.Node;
import work.llm.map.integration.service.IdService;
import work.llm.map.integration.service.TileService;
import work.llm.map.service.link.LinkService;
import work.llm.map.service.node.NodeService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CommonService {

    @Resource
    private IdService idService;
    @Resource
    private TileService tileService;
    @Resource
    private LinkService linkService;
    @Resource
    private NodeService nodeService;

    public Long generateId(String datasource) {
        return this.generateIds(datasource, 1).get(0);
    }

    public List<Long> generateIds(String datasource, int size) {
        if (datasource.contains(",")) {
            String[] sources = datasource.split(",");
            datasource = sources[sources.length - 1];
        }
        try {
            return idService.generateIds(datasource, size);
        } catch (Exception e) {
            log.error("ID生成错误", e);
            throw new BizException("ID Generation Error");
        }
    }

    public String datasource(String req, String old) {

        if (StringUtils.isBlank(old)) {
            return req;
        } else if (old.contains(req)) {
            return old;
        } else {
            List<Integer> datasource = new ArrayList<>();
            for (String dbs : old.split(",")) {
                datasource.add(Integer.parseInt(dbs));
            }
            for (String dbs : req.split(",")) {
                datasource.add(Integer.parseInt(dbs));
            }
            return datasource.stream().map(String::valueOf).distinct().collect(Collectors.joining(","));
        }
    }

    public Set<Integer> calculateRandoms(int total, int extractNumber) {
        Set<Integer> randoms = new HashSet<>();
        if (extractNumber >= total) {
            for (int i = 0; i < total; i++) {
                randoms.add(i);
            }
        } else {
            int random;
            // 抽取的索引
            while (randoms.size() < extractNumber) {
                random = (int) (Math.random() * total);
                randoms.add(random);
            }
        }
        return randoms;
    }

    public int calculateExtractNumber(int total, String ratio) {
        // 计算抽取比例
        return new BigDecimal(total).multiply(new BigDecimal(ratio))
                .setScale(0, RoundingMode.CEILING).intValue();
    }

    public void checkReq(DatasourceAndTaskIdReq req) {
        if (StringUtils.isBlank(req.getTaskId())) {
            throw new BizException("Request parameter error: [taskId] is empty.");
        }
        if (StringUtils.isBlank(req.getDatasource())) {
            throw new BizException("Request parameter error: [datasource] is empty.");
        }
    }

    public String getTileId(String geometry) {
        try {
            return tileService.getTileId(geometry);
        } catch (Exception e) {
            log.error("H3网格计算错误", e);
            throw new BizException("H3 Grid Calculation Error");
        }
    }

    public void links(List<? extends LinksNodeBase> bases) {
        if (CollectionUtils.isEmpty(bases)) return;

        Set<String> hllLinkids = new HashSet<>();
        for (LinksNodeBase base : bases) {
            String inlinkId = base.getInlinkId();
            String pass = base.getPass();
            String outlinkId = base.getOutlinkId();
            if (StringUtils.isNotBlank(inlinkId)) hllLinkids.add(inlinkId);
            if (StringUtils.isNotBlank(pass)) {
                hllLinkids.addAll(Arrays.stream(pass.split("\\|")).collect(Collectors.toList()));
            }
            if (StringUtils.isNotBlank(outlinkId)) hllLinkids.add(outlinkId);
        }

        List<String> hllNodeids = bases.stream().map(LinksNodeBase::getNodeId).collect(Collectors.toList());

        Map<String, Link> linkMap = this.linkService.queryMapByLinkids(new ArrayList<>(hllLinkids));
        Map<String, Node> nodeMap = this.nodeService.queryMapByNodeids(hllNodeids);

        bases.forEach(base -> {
            base.setLinks(this.links(base.getInlinkId(), base.getOutlinkId(), base.getPass(), linkMap));
            base.setNode(this.node(base.getNodeId(), nodeMap));
        });
    }

    public List<LinkVo> links(String inlinkId, String outlinkId, String pass, Map<String, Link> linkMap) {
        if (CollectionUtils.isEmpty(linkMap)) return null;

        List<String> hllLinkIds = new LinkedList<>();

        if (Objects.nonNull(inlinkId)) hllLinkIds.add(inlinkId);
        if (StringUtils.isNotBlank(pass))
            hllLinkIds.addAll(Arrays.stream(pass.split("\\|")).collect(Collectors.toList()));
        if (Objects.nonNull(outlinkId)) hllLinkIds.add(outlinkId);

        List<LinkVo> links = new ArrayList<>();
        for (String hllLinkid : hllLinkIds) {
            Link link = linkMap.get(hllLinkid);
            //if (link.getStatus().equals(Const.STATUS_DELETED)) continue;
            links.add(new LinkVo(link));
        }

        return links;
    }

    public NodeVo node(String hllNodeid, Map<String, Node> nodeMap) {
        Node node = nodeMap.get(hllNodeid);
        //if (node.getStatus().equals(Const.STATUS_DELETED)) return null;
        return new NodeVo(node);
    }

    public int ruleInfo(Link inLink, Link outLink, Link inLinkNext, Link outLinkBefore) {
        double angle = calculateAngle(inLink, outLink, inLinkNext, outLinkBefore);
        int type;
        if (angle > 135 && angle <= 225) {
            type = Const.RULE_INFO_TURN;
        } else if (angle > 225 && angle <= 315) {
            type = Const.RULE_INFO_LEFT;
        } else if ((angle > 315 && angle <= 360) || (angle >= 0 && angle <= 45)) {
            type = Const.RULE_INFO_FORWARD;
        } else {
            type = Const.RULE_INFO_RIGHT;
        }
        return type;
    }

    public Double calculateAngle(Link inLink, Link outLink, Link inLinkNext, Link outLinkBefore) {
        Geometry inLinkGeom = inLink.getGeometry();
        Geometry outLinkGeom = outLink.getGeometry();

        String inLinkDir = inLink.getDir();
        String outLinkDir = outLink.getDir();

        if (inLinkDir.equals(Const.LINK_DIR_UNKNOWN) || inLinkDir.equals(Const.LINK_DIR_DOUBLE)) {
            if (inLink.getHllSNid().equals(inLinkNext.getHllSNid()) || inLink.getHllSNid().equals(inLinkNext.getHllENid())) {
                inLinkGeom = inLinkGeom.reverse();
            }
        }
        if (outLinkDir.equals(Const.LINK_DIR_UNKNOWN) || outLinkDir.equals(Const.LINK_DIR_DOUBLE)) {
            if (outLink.getHllENid().equals(outLinkBefore.getHllSNid()) || outLink.getHllENid().equals(outLinkBefore.getHllENid())) {
                outLinkGeom = outLinkGeom.reverse();
            }
        }
        if (inLinkDir.equals(Const.LINK_DIR_BACKWARD)) {
            inLinkGeom = inLinkGeom.reverse();
        }

        if (outLinkDir.equals(Const.LINK_DIR_BACKWARD)) {
            outLinkGeom = outLinkGeom.reverse();
        }

        if (inLinkDir.equals(Const.LINK_DIR_DOUBLE_NO) || outLinkDir.equals(Const.LINK_DIR_DOUBLE_NO)) {
            return Double.MAX_VALUE;
        }

        Coordinate[] inLinkCoordinates = inLinkGeom.getCoordinates();
        Coordinate[] outLinkCoordinates = outLinkGeom.getCoordinates();

        Coordinate[] inLinkCoordinate = new Coordinate[]{inLinkCoordinates[inLinkCoordinates.length - 2], inLinkCoordinates[inLinkCoordinates.length - 1]};
        Coordinate[] outLinkCoordinate = new Coordinate[]{outLinkCoordinates[0], outLinkCoordinates[1]};

        double inLink_northBased_arc = this.azimuth(inLinkCoordinate[0], inLinkCoordinate[1]);
        double inLink_northBased_angle = inLink_northBased_arc * 180 / Math.PI;

        double outLink_northBased_arc = this.azimuth(outLinkCoordinate[0], outLinkCoordinate[1]);
        double outLink_northBased_angle = outLink_northBased_arc * 180 / Math.PI;
        double angle = outLink_northBased_angle - inLink_northBased_angle;
        if (angle < 0) {
            angle = 360 + angle;
        }
        return angle;
    }


    public double azimuth(Coordinate from, Coordinate to) {
        double dx = to.x - from.x;
        double dy = to.y - from.y;

        // 计算相对于正北方向的方位角（顺时针）
        double azimuth = Math.atan2(dx, dy);

        // 如果角度为负，则转换为正值（确保在 0 ~ 2π 范围内）
        if (azimuth < 0) {
            azimuth += 2 * Math.PI;
        }

        return azimuth;
    }
}
