package work.llm.map.service.node.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import work.llm.map.dao.node.entity.Node;

import java.util.ArrayList;
import java.util.List;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "路口操作响应类")
public class CrossingOperateRes {

    @Schema(description = "主点信息")
    private Node mainNode;
    @Schema(description = "子点信息集合")
    private List<Node> subNodes = new ArrayList<>();
}
