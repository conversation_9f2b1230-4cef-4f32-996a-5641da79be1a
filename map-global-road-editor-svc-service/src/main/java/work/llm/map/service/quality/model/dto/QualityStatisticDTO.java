package work.llm.map.service.quality.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "质检统计类")
public class QualityStatisticDTO {
    @Schema(description = "质检准确率")
    private BigDecimal accuracyRatio = new BigDecimal(0);
    @Schema(description = "质检正确数量")
    private Integer correctNumber = 0;
    @Schema(description = "错误数量（多错漏）")
    private Integer errorNumber = 0;
    @Schema(description = "全部数量（多错漏正无）")
    private Integer totalNumber = 0;

}
