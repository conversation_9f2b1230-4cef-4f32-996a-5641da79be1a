package work.llm.map.service.scheduler;

import cn.huolala.arch.hermes.api.annotation.HermesReference;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.lalaframework.dynamic.datasource.annotation.DSTransactional;
import cn.lalaframework.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import cn.lalaframework.job.sdk.HllXxlJobManager;
import cn.lalaframework.job.sdk.annotation.HllXxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import work.llm.map.common.constant.Const;
import work.llm.map.common.utils.JsonUtils;
import work.llm.map.common.utils.MapperUtils;
import work.llm.map.dao.common.entity.TaskCreator;
import work.llm.map.dao.matlink.entity.MatLink;
import work.llm.map.facade.model.RoadCreateTaskRequest;
import work.llm.map.facade.service.RoadTaskEditFacade;
import work.llm.map.service.matlink.MatLinkService;
import work.llm.map.service.task.TaskService;
import work.llm.map.service.task.model.req.TaskCreateReq;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@SuppressWarnings({"Duplicates", "unused"})
public class MatLinkTaskCreateJobHandler {

    @Resource
    private MatLinkService matLinkService;
    @Resource
    private TaskService taskService;
    @HermesReference(value = "${inner.facade.task}")
    private RoadTaskEditFacade roadTaskEditFacade;

    @HllXxlJob(value = "matLinkTaskCreateJobHandler")
    @DSTransactional
    public void execute() {
        String param = HllXxlJobManager.getJobParam();
        if (StrUtil.isEmpty(param)) return;

        String[] parameters = param.split(Const.SYMBOL_COMMA);
        for (String parameter : parameters) {
            String old = DynamicDataSourceContextHolder.peek();
            try {
                DynamicDataSourceContextHolder.push(parameter);

                List<TaskCreator> tasks = matLinkService.getBaseMapper().taskCreatable();
                if (CollUtil.isEmpty(tasks)) continue;

                for (TaskCreator taskCreator : tasks) {
                    try {
                        List<MatLink> matLinks = this.matLinkService.lambdaQuery().eq(MatLink::getTaskCreated, Const.NO)
                                .eq(MatLink::getDsId, taskCreator.getDsId()).list();

                        if (CollUtil.isEmpty(matLinks)) continue;
                        List<String> tileIds = matLinks.stream().map(MatLink::getTileId).distinct().collect(Collectors.toList());
                        taskCreator.setTileId(CollUtil.join(tileIds, ","));
                        taskCreator.setVersion(LocalDate.now().toString());

                        String taskId = createTask(taskCreator);
                        if (StrUtil.isEmpty(taskId)
                            || taskId.equalsIgnoreCase("null")) continue;
                        // 设置任务id
                        taskCreator.setTaskId(taskId);
                        this.taskService.create(MapperUtils.map(taskCreator, TaskCreateReq.class));
                        // 更新资料状态
                        this.matLinkService.lambdaUpdate()
                                .eq(MatLink::getCollectVersion, taskCreator.getCollectVersion())
                                .eq(MatLink::getDsId, taskCreator.getDsId())
                                .eq(MatLink::getTaskCreated, Const.NO)
                                .set(MatLink::getTaskCreated, Const.YES)
                                .set(MatLink::getTaskCreateTime, LocalDateTime.now())
                                .set(MatLink::getUpdateTime, LocalDateTime.now())
                                .set(MatLink::getTaskId, taskId)
                                .update();
                    } catch (Exception e) {
                        log.error("创建任务失败,collect_version=[{}],ds_id=[{}]", taskCreator.getCollectVersion(), taskCreator.getDsId(), e);
                    }
                }
            } finally {
                if (StrUtil.isNotBlank(old)) DynamicDataSourceContextHolder.push(old);
            }
        }

    }

    private String createTask(TaskCreator taskCreator) {
        RoadCreateTaskRequest request = MapperUtils.map(taskCreator, RoadCreateTaskRequest.class);
        log.info("创建任务请求参数,request=[{}]", JsonUtils.toJsonString(request));
        Long id = roadTaskEditFacade.create(request);
        return String.valueOf(id);
    }
}
