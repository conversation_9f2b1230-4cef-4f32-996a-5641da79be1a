package work.llm.map.service.taskcounter.service;

import cn.hutool.core.collection.CollUtil;
import cn.lalaframework.dynamic.datasource.annotation.DS;
import cn.lalaframework.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import work.llm.map.dao.taskcounter.entity.TaskCounter;
import work.llm.map.dao.taskcounter.mapper.TaskCounterMapper;

import java.util.List;

@Service
@DS("task")
@SuppressWarnings("all")
public class TaskCounterService extends ServiceImpl<TaskCounterMapper, TaskCounter> {

    @DSTransactional
    public void updateCounter(List<TaskCounter> taskCounters) {
        if (CollUtil.isEmpty(taskCounters)) return;
        this.saveOrUpdateBatch(taskCounters);
    }
}
