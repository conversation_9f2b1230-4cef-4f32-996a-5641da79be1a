package work.llm.map.service.datamining;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import work.llm.map.dao.datamining.entity.DataminingPolygon;
import work.llm.map.dao.datamining.mapper.DataminingPolygonMapper;

import java.util.List;

@Service
public class DataminingPolygonService extends ServiceImpl<DataminingPolygonMapper, DataminingPolygon> {

    public List<DataminingPolygon> queryByExtent(String extent, List<Long> groupIds) {
        return this.lambdaQuery().in(DataminingPolygon::getGroupId, groupIds)
                .apply(StrUtil.isNotBlank(extent), "ST_Intersects(ST_GeomFromText({0}, 4326), geometry)", extent).list();
    }
}
