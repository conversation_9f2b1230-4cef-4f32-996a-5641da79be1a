package work.llm.map.service.datamining;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import work.llm.map.common.constant.Const;
import work.llm.map.common.exception.BizException;
import work.llm.map.dao.datamining.entity.*;
import work.llm.map.service.datamining.model.req.DataminingStatusReq;
import work.llm.map.service.quality.QualityService;
import work.llm.map.service.quality.model.dto.QualityStatisticDTO;
import work.llm.map.service.task.model.res.TaskWorkInfoRes;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class DataminingService {

    @Resource
    private DataminingGroupService groupService;
    @Resource
    private DataminingPointService pointService;
    @Resource
    private DataminingLineService lineService;
    @Resource
    private DataminingPolygonService polygonService;
    @Resource
    private QualityService qualityService;

    public List<DataminingGroup> queryByExtent(String taskId, String extent) {
        List<DataminingGroup> groups = this.groupService.lambdaQuery().eq(DataminingGroup::getTaskId, taskId).list();
        if (CollUtil.isEmpty(groups)) return null;

        Map<Long, List<DataminingGeometry>> dgsMap = getDgsMap(extent, groups);

        // 过滤掉groups中在dgsMap中没有value的组
        return groups.stream().filter(group -> dgsMap.containsKey(group.getGroupId()))
                .peek(group -> group.setDgs(dgsMap.get(group.getGroupId())))
                .collect(Collectors.toList());
    }

    public DataminingGroup queryById(Long id) {
        DataminingGroup group = this.groupService.lambdaQuery().eq(DataminingGroup::getGroupId, id).one();
        if (ObjUtil.isNull(group)) return null;

        this.getAndSetDgs(CollUtil.newArrayList(group));

        return group;
    }

    public boolean batchStatus(DataminingStatusReq req) {
        List<Long> groupIds = req.getGroupIds();
        if (CollUtil.isEmpty(groupIds)) return false;
        Integer checkStatus = req.getCheckStatus();
        String invalidStatus = req.getInvalidStatus();
        String invalidStatusText = req.getInvalidStatusText();
        return this.groupService.lambdaUpdate().in(DataminingGroup::getGroupId, groupIds)
                .set(ObjUtil.isNotNull(checkStatus), DataminingGroup::getCheckStatus, checkStatus)
                .set(ObjUtil.isNotNull(invalidStatus), DataminingGroup::getInvalidStatus, invalidStatus)
                .set(StrUtil.isNotBlank(invalidStatusText), DataminingGroup::getInvalidStatusText, invalidStatusText)
                .set(DataminingGroup::getEditFlag, Const.YES)
                .update();
    }

    public DataminingGroup queryOne(String taskId) {
        List<DataminingGroup> groups = this.all(taskId);

        Optional<DataminingGroup> firstUndoOptional = groups.stream().filter(item -> item.getEditFlag().equals(Const.NO))
                .findFirst();
        DataminingGroup group = firstUndoOptional.orElseGet(() -> groups.get(0));
        this.getAndSetDgs(CollUtil.newArrayList(group));

        Long groupId = group.getGroupId();
        int index = groups.indexOf(groups.stream().filter(item -> item.getGroupId().equals(groupId)).findFirst()
                .orElseThrow(() -> new BizException("Can not find the material[" + groupId + "] in current task.")));
        setCurrenPosition(group, index, groups.size());

        return group;
    }

    public DataminingGroup queryCurrent(String taskId, Long groupId, Integer findType) {
        List<DataminingGroup> groups = this.all(taskId);
        // 找出groupId在groups中的位置
        int index = groups.indexOf(groups.stream().filter(item -> item.getGroupId().equals(groupId)).findFirst()
                .orElseThrow(() -> new BizException("Can not find the material[" + groupId + "] in current task.")));
        if (Const.YES.equals(findType)) {
            index = Math.min(index + 1, groups.size() - 1);
        }
        if (Const.NO.equals(findType)) {
            index = Math.max(index - 1, 0);
        }
        DataminingGroup group = groups.get(index);
        setCurrenPosition(group, index, groups.size());
        this.getAndSetDgs(CollUtil.newArrayList(group));
        return group;
    }


    public void setCurrenPosition(DataminingGroup group, int index, int size) {
        if (index == 0) group.setCurrentPosition("first");
        else if (index == size - 1) group.setCurrentPosition("last");
    }

    public List<DataminingGroup> all(String taskId) {
        List<DataminingGroup> groups = this.groupService.lambdaQuery().eq(DataminingGroup::getTaskId, taskId)
                .orderByAsc(DataminingGroup::getGroupId).list();
        if (CollectionUtils.isEmpty(groups)) throw new BizException("Can not find any materials in current task.");
        return groups;
    }

    public void getAndSetDgs(List<DataminingGroup> groups) {
        Map<Long, List<DataminingGeometry>> dgsMap = this.getDgsMap(null, groups);
        groups.forEach(group -> group.setDgs(dgsMap.get(group.getGroupId())));
    }

    public Map<Long, List<DataminingGeometry>> getDgsMap(String extent, List<DataminingGroup> groups) {
        List<Long> groupIds = groups.stream().map(DataminingGroup::getGroupId).distinct().collect(Collectors.toList());
        // 查询点
        List<DataminingPoint> points = this.pointService.queryByExtent(extent, groupIds);
        // 查询线
        List<DataminingLine> lines = this.lineService.queryByExtent(extent, groupIds);
        // 查询面
        List<DataminingPolygon> polygons = this.polygonService.queryByExtent(extent, groupIds);
        // 将流中的每个元素合并到一起
        List<? extends DataminingGeometry> geometries = Stream
                .of(points, lines, polygons).flatMap(List::stream).collect(Collectors.toList());
        // 根据group分组
        return geometries.stream().collect(Collectors.groupingBy(DataminingGeometry::getGroupId,
                Collectors.collectingAndThen(Collectors.toList(), list
                        -> list.stream().sorted(Comparator.comparing(DataminingGeometry::getSeqNum)).collect(Collectors.toList()))));
    }

    public TaskWorkInfoRes workInfo(String taskId) {
        List<DataminingGroup> groups = this.groupService.lambdaQuery()
                .eq(DataminingGroup::getTaskId, taskId)
                .list();
        // 未作业量
        long undo = groups.stream().filter(m -> m.getEditFlag().equals(Const.NO)).count();
        // 质检相关
        QualityStatisticDTO qualityStatistic = qualityService.statistic(taskId);
        return TaskWorkInfoRes.builder()
                .undoNumber(undo)
                .displayNumber(String.valueOf(groups.size()))
                .qualityAccuracy(qualityStatistic.getAccuracyRatio().toString())
                .build();
    }

    public List<DataminingGroup> startQualityCheck(String taskId) {
        List<DataminingGroup> groups = this.groupService.lambdaQuery().eq(DataminingGroup::getTaskId, taskId).list();
        if (CollUtil.isEmpty(groups))
            throw new BizException("Failed to start quality check: Data not found. Please check your input and try again.");
        getAndSetDgs(groups);
        return groups;
    }

    public void workSubmitCheck(String taskId) {
        List<DataminingGroup> groups = this.groupService.lambdaQuery().eq(DataminingGroup::getTaskId, taskId).list();
        if (CollUtil.isEmpty(groups))
            throw new BizException("Failed to submit the job: Data not found. Please check your input and try again.");
        Optional<DataminingGroup> any = groups.stream().filter(matLink -> Objects.equals(matLink.getEditFlag(), Const.NO)).findAny();
        if (any.isPresent())
            throw new BizException("Failed to submit the job: Some data entries have not been processed. Please ensure all tasks are completed and try again.");
    }
}
