package work.llm.map.service.user.impl;

import work.llm.map.common.utils.MapperUtils;
import work.llm.map.dao.user.UserDao;
import work.llm.map.dao.user.entity.UserPO;
import work.llm.map.service.user.UserService;
import work.llm.map.service.user.model.UserDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/12/1
 */
@Service
public class UserServiceImpl implements UserService {

    @Resource
    private UserDao userDao;

    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 创建用户
     * @param userDo 用户信息
     */
    @Override
    public void createUser(UserDO userDo) {
        UserPO userPo = MapperUtils.map(userDo, UserPO.class);

        //事务最小化示例
        transactionTemplate.execute(status -> {
            userDao.insert(userPo);
            // 其他数据库操作

            return null;
        });
    }

    /**
     * 根据id查找
     * @param id id
     * @return {@link UserDO}
     */
    @Override
    public UserDO getById(Long id) {
        UserPO userPo = userDao.getById(id);
        return MapperUtils.map(userPo, UserDO.class);
    }

}