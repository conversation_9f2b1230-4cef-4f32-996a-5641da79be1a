package work.llm.map.service.matlink.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.vividsolutions.jts.geom.Geometry;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import work.llm.map.common.jackson.deserializer.StringToGeometryDeserializer;
import work.llm.map.common.jackson.serializer.GeometryToStringSerializer;
import work.llm.map.dao.matlink.entity.MatLink;

@Data
@Schema(description = "路网资料全部数据响应类")
public class MatLinkAllRes {

    public MatLinkAllRes(MatLink matLink) {
        BeanUtils.copyProperties(matLink, this);
    }

    @Schema(description = "资料ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long matId;
    @Schema(description = "道路ID")
    private String hllLinkid;
    @Schema(description = "作业状态 0-未作业 1-已作业 2-已删除")
    private Integer workStatus;
    @JsonProperty("geom")
    @Schema(description = "坐标")
    @JsonSerialize(using = GeometryToStringSerializer.class)
    @JsonDeserialize(using = StringToGeometryDeserializer.class)
    private Geometry geometry;
}
