package work.llm.map.service.matlink;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import work.llm.map.common.constant.Const;
import work.llm.map.common.exception.BizException;
import work.llm.map.dao.matlink.entity.MatLink;
import work.llm.map.dao.matlink.mapper.MatLinkMapper;
import work.llm.map.service.common.CommonService;
import work.llm.map.service.link.model.templete.LinkTemplate;
import work.llm.map.service.matlink.model.req.MatLinkStatusReq;
import work.llm.map.service.matlink.model.res.MatLinkAllRes;
import work.llm.map.service.quality.QualityService;
import work.llm.map.service.quality.model.dto.QualityStatisticDTO;
import work.llm.map.service.task.model.res.TaskWorkInfoRes;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MatLinkService extends ServiceImpl<MatLinkMapper, MatLink> {

    @Resource
    private CommonService commonService;
    @Resource
    private QualityService qualityService;

    public MatLink template(String datasource) {
        MatLink template = new MatLink();
        LinkTemplate linkTemplate = new LinkTemplate();
        BeanUtils.copyProperties(linkTemplate, template);
        template.setUpdateTime(LocalDateTime.now());
        template.setGeometry(null);
        template.setDatasource(null);
        template.setWorkStatus(Const.WORK_STATUS_DRAW);
        template.setHllLinkid(String.valueOf(commonService.generateId(datasource)));
        return template;
    }

    public List<MatLink> queryByExtent(String taskId, String extent) {
        return this.lambdaQuery().eq(MatLink::getTaskId, taskId)
                .apply("ST_Intersects(ST_GeomFromText({0}, 4326), geometry)", extent).list();
    }

    public MatLink queryUndo(String taskId) {
        List<MatLink> matLinks = this.lambdaQuery().eq(MatLink::getTaskId, taskId)
                .eq(MatLink::getWorkStatus, Const.WORK_STATUS_DEFAULT)
                .orderByAsc(MatLink::getMatId).list();
        if (CollUtil.isEmpty(matLinks))
            throw new BizException("All tasks related to the current dataset have been completed.");
        return matLinks.get(0);
    }

    public boolean batchStatus(MatLinkStatusReq req) {
        return this.lambdaUpdate().in(MatLink::getMatId, req.getMatIds())
                .set(MatLink::getWorkStatus, req.getWorkStatus())
                .set(MatLink::getUpdateTime, LocalDateTime.now())
                .update();
    }


    public List<MatLinkAllRes> all(String taskId) {
        return this.lambdaQuery().eq(MatLink::getTaskId, taskId)
                .ne(MatLink::getWorkStatus, Const.WORK_STATUS_DRAW)
                .list()
                .stream().map(MatLinkAllRes::new)
                .sorted(Comparator.comparing(MatLinkAllRes::getMatId))
                .collect(Collectors.toList());
    }

    public Integer findIdx(String taskId, Long currentMatId, Integer findType) {
        List<MatLinkAllRes> matGeoms = this.all(taskId);
        // 现状是matGeoms按照matId排序
        MatLinkAllRes matGeom;
        MatLinkAllRes currentMatGeom = null;
        int currentMatGeomIndex = 0;
        if (Const.YES.equals(findType)) {
            // 当前的matGeom
            // 从左到右找到第一个workStatus=0的索引
            for (int i = 0; i < matGeoms.size(); i++) {
                matGeom = matGeoms.get(i);
                if (matGeom.getMatId().equals(currentMatId)) {
                    currentMatGeom = matGeom;
                    currentMatGeomIndex = i;
                }
                if (matGeom.getWorkStatus() == Const.WORK_STATUS_DEFAULT && matGeom.getMatId().compareTo(currentMatId) > 0) {
                    return i;
                }
            }
        } else if (Const.NO.equals(findType)) {
            // 将所有小于currentMatId的matId找出来
            for (int i = 0; i < matGeoms.size(); i++) {
                matGeom = matGeoms.get(i);
                if (matGeom.getMatId().equals(currentMatId)) {
                    currentMatGeom = matGeom;
                    currentMatGeomIndex = i;
                }
                if (matGeom.getWorkStatus() == Const.WORK_STATUS_DEFAULT && matGeom.getMatId().compareTo(currentMatId) < 0) {
                    return i;
                }
            }
        }
        if (Objects.nonNull(currentMatGeom) && currentMatGeom.getWorkStatus() == Const.WORK_STATUS_DEFAULT)
            return currentMatGeomIndex;
        throw new BizException("All tasks related to the current dataset have been completed.");
    }

    public void workSubmitCheck(String taskId) {
        List<MatLink> matLinks = this.lambdaQuery().eq(MatLink::getTaskId, taskId).list();
        if (CollUtil.isEmpty(matLinks))
            throw new BizException("Failed to submit the job: Data not found. Please check your input and try again.");
        Optional<MatLink> any = matLinks.stream().filter(matLink -> matLink.getWorkStatus() == Const.WORK_STATUS_DEFAULT).findAny();
        if (any.isPresent())
            throw new BizException("Failed to submit the job: Some data entries have not been processed. Please ensure all tasks are completed and try again.");
    }

    public List<MatLink> startQualityCheck(String taskId) {
        List<MatLink> matLinks = this.lambdaQuery().eq(MatLink::getTaskId, taskId).list();
        if (CollUtil.isEmpty(matLinks))
            throw new BizException("Failed to start quality check: Data not found. Please check your input and try again.");
        return matLinks;
    }

    public BigDecimal calculateEffectiveRate(String taskId) {
        List<MatLink> matLinks = this.lambdaQuery().eq(MatLink::getTaskId, taskId)
                .in(MatLink::getWorkStatus, CollUtil.toList(Const.WORK_STATUS_WORKED, Const.WORK_STATUS_DELETED)).list();
        if (CollectionUtils.isEmpty(matLinks)) return new BigDecimal(0);
        // 分母数 已作业+删除
        BigDecimal denominator = new BigDecimal(matLinks.size()).setScale(2, RoundingMode.HALF_UP);
        // 分子数 已作业
        long workedNumber = matLinks.stream().filter(m -> m.getWorkStatus() == Const.WORK_STATUS_WORKED).count();
        BigDecimal numerator = new BigDecimal(workedNumber).setScale(2, RoundingMode.HALF_UP);
        return numerator.divide(denominator, 2, RoundingMode.HALF_UP);
    }

    public TaskWorkInfoRes workInfo(String taskId) {
        List<MatLink> matLinks = this.lambdaQuery().eq(MatLink::getTaskId, taskId).list();
        // 未作业量
        long undo = matLinks.stream().filter(m -> m.getWorkStatus().equals(Const.WORK_STATUS_DEFAULT)).count();

        // 自画线数量
        long draw = matLinks.stream().filter(m -> m.getWorkStatus().equals(Const.WORK_STATUS_DRAW)).count();
        long total = matLinks.size();

        QualityStatisticDTO qualityStatistic = qualityService.statistic(taskId);

        return TaskWorkInfoRes.builder()
                .undoNumber(undo)
                .displayNumber((total - draw) + Const.SYMBOL_PIPE + total)
                .qualityAccuracy(qualityStatistic.getAccuracyRatio().toString())
                .build();
    }
}
