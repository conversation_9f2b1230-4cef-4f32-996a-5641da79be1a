package work.llm.map.service.quality.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "质检分页列表请求类")
public class QualityPageReq {

    private static final String ROLE_CODE_EDIT_WORKER = "EDIT_WORKER";
    private static final String ROLE_CODE_QUALITY_CHECKER = "QUALITY_CHECKER";
    private static final String ROLE_CODE_VERIFIER = "VERIFIER";

    public boolean qc() {
        return ROLE_CODE_QUALITY_CHECKER.equalsIgnoreCase(roleCode);
    }
    public boolean ew() {
        return ROLE_CODE_EDIT_WORKER.equalsIgnoreCase(roleCode);
    }

    @Schema(description = "页数")
    private Integer pageNum;
    @Schema(description = "页容")
    private Integer pageSize;
    @Schema(description = "任务ID")
    private String taskId;
    @Schema(description = "角色编码")
    private String roleCode = "QUALITY_CHECKER";
}
