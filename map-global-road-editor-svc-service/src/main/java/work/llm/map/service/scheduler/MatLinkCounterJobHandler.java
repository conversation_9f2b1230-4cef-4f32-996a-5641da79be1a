package work.llm.map.service.scheduler;

import cn.hutool.core.util.StrUtil;
import cn.lalaframework.dynamic.datasource.annotation.DSTransactional;
import cn.lalaframework.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import cn.lalaframework.job.sdk.HllXxlJobManager;
import cn.lalaframework.job.sdk.annotation.HllXxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import work.llm.map.common.constant.Const;
import work.llm.map.dao.taskcounter.entity.TaskCounter;
import work.llm.map.service.taskcounter.service.TaskCounterService;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class MatLinkCounterJobHandler {

    @Resource
    private TaskCounterService taskCounterService;

    @HllXxlJob(value = "matLinkCounterJobHandler")
    @DSTransactional
    public void execute() {
        String param = HllXxlJobManager.getJobParam();
        if (StrUtil.isEmpty(param)) return;

        String[] parameters = param.split(Const.SYMBOL_COMMA);
        for (String parameter : parameters) {
            String old = DynamicDataSourceContextHolder.peek();
            try {
                DynamicDataSourceContextHolder.push(parameter);
                // 查询计数数据
                List<TaskCounter> taskCounters = taskCounterService.getBaseMapper().countGroupByTaskId();
                // 保存
                taskCounterService.updateCounter(taskCounters);
            } finally {
                if (StrUtil.isNotBlank(old)) DynamicDataSourceContextHolder.push(old);
            }
        }

    }
}
