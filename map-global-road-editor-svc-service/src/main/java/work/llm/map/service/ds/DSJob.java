package work.llm.map.service.ds;

import cn.lalaframework.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import work.llm.map.common.constant.Const;
import work.llm.map.core.mybatisplus.DynamicDatasourceManager;
import work.llm.map.dao.ds.entity.DS;

import javax.annotation.Resource;
import java.util.List;

@Component
public class DSJob {

    @Resource
    private DynamicDatasourceManager dynamicDatasourceManager;
    @Resource
    private DSService dsService;
    @Value("${spring.profiles.active}")
    private String profiles;

    // 每2秒
    @Scheduled(cron = "*/10 * * * * ?")
    public void execute() {
        LambdaQueryChainWrapper<DS> lambdaQuery = dsService.lambdaQuery();
        if (profiles.contains("fast")) lambdaQuery.eq(DS::getEnv, "fast");
        else lambdaQuery.eq(DS::getEnv, "normal");
        List<DS> list = lambdaQuery.list();
        for (DS ds : list) {
            if (ds.getStatus().equals(Const.NO)) {
                dynamicDatasourceManager.remove(ds.getName());
                continue;
            }
            DataSourceProperty property = new DataSourceProperty();
            property.setUrl("jdbc:p6spy:postgresql://" + ds.getIp() + ":" + ds.getPort() + "/" + ds.getDbname());
            property.setUsername(ds.getUsername());
            property.setPassword(ds.getPassword());
            property.setDriverClassName("com.p6spy.engine.spy.P6SpyDriver");
            dynamicDatasourceManager.add(ds.getName(), property);
        }
    }
}
