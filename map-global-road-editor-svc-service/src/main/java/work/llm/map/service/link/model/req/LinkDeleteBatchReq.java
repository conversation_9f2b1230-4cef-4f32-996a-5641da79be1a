package work.llm.map.service.link.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import work.llm.map.dao.link.entity.Link;
import work.llm.map.service.common.DatasourceAndTaskIdReq;

import java.util.List;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "删除道路请求")
public class LinkDeleteBatchReq extends DatasourceAndTaskIdReq {

    @Schema(description = "待删除道路信息")
    private List<Link> links;
}
