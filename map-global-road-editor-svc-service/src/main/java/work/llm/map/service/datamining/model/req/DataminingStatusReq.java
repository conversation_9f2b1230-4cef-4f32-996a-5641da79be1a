package work.llm.map.service.datamining.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
@ApiModel(description = "资料核实请求类")
public class DataminingStatusReq {

    @ApiModelProperty("资料ID")
    private List<Long> groupIds;
    @ApiModelProperty("核实状态")
    private Integer checkStatus;
    @ApiModelProperty("无效状态")
    private String invalidStatus;
    @ApiModelProperty("无效备注")
    private String invalidStatusText;
}
