package work.llm.map.service.link.model.req;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.vividsolutions.jts.geom.Geometry;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import work.llm.map.common.jackson.deserializer.StringToGeometryDeserializer;
import work.llm.map.common.jackson.serializer.GeometryToStringSerializer;
import work.llm.map.service.common.DatasourceAndTaskIdReq;

import java.util.List;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "道路形状更新请求")
public class LinkShapeUpdateReq extends DatasourceAndTaskIdReq {

    @Schema(description = "道路形状")
    private List<LinkReq> links;

    @Data
    public static class LinkReq {
        private String hllLinkid;
        @JsonSerialize(using = GeometryToStringSerializer.class)
        @JsonDeserialize(using = StringToGeometryDeserializer.class)
        private Geometry geom;
        private String datasource;
        private Integer olv;
    }
}
