package work.llm.map.service.node;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.lalaframework.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.vividsolutions.jts.geom.*;
import com.vividsolutions.jts.operation.distance.DistanceOp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import work.llm.map.common.constant.Const;
import work.llm.map.common.exception.BizException;
import work.llm.map.common.utils.ExtentUtil;
import work.llm.map.common.utils.OlvUtil;
import work.llm.map.common.utils.StatusUtil;
import work.llm.map.dao.link.entity.Link;
import work.llm.map.dao.node.entity.Node;
import work.llm.map.dao.node.mapper.NodeMapper;
import work.llm.map.service.common.CommonService;
import work.llm.map.service.link.LinkService;
import work.llm.map.service.node.model.req.CrossingOperateReq;
import work.llm.map.service.node.model.req.NodeAttributeUpdateReq;
import work.llm.map.service.node.model.res.CrossingOperateRes;
import work.llm.map.service.node.model.res.CrossingRenderRes;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NodeService extends ServiceImpl<NodeMapper, Node> {

    @Resource
    private CommonService commonService;
    @Resource
    private LinkService linkService;
    private final GeometryFactory geometryFactory = new GeometryFactory();

    public List<Node> queryByExtent(String extent, String status) {
        ExtentUtil.exceed(extent);

        List<Node> nodes = this.lambdaQuery().in(Node::getStatus, StatusUtil.status(status))
                .apply("ST_Intersects(ST_GeomFromText({0}, 4326), geometry)", extent).list();

        if (!CollUtil.isEmpty(nodes)) {
            List<String> hllNodeids = nodes.stream().map(Node::getHllNodeid).distinct().collect(Collectors.toList());
            List<Link> connectLinks = this.linkService.lambdaQuery()
                    .ne(Link::getStatus, 1)
                    .and(q -> q.in(Link::getHllSNid, hllNodeids).or().in(Link::getHllENid, hllNodeids))
                    .list();

            Map<String, List<Link>> snodeMap = connectLinks.stream().collect(Collectors.groupingBy(Link::getHllSNid));
            Map<String, List<Link>> enodeMap = connectLinks.stream().collect(Collectors.groupingBy(Link::getHllENid));

            List<Link> empty = CollUtil.newArrayList();
            String hllNodeid;
            int sConnectLinkNum;
            int eConnectLinkNum;
            for (Node node : nodes) {
                hllNodeid = node.getHllNodeid();
                sConnectLinkNum = snodeMap.getOrDefault(hllNodeid, empty).size();
                eConnectLinkNum = enodeMap.getOrDefault(hllNodeid, empty).size();
                node.setConnectLinkNum(sConnectLinkNum + eConnectLinkNum);
            }
        }
        return nodes;
    }

    public Node queryById(String hllNodeid, String status) {
        return this.lambdaQuery().eq(Node::getHllNodeid, hllNodeid)
                .in(Node::getStatus, StatusUtil.status(status))
                .one();
    }

    @SuppressWarnings("Duplicates")
    @DSTransactional
    public Node updateAttribute(NodeAttributeUpdateReq req) {
        Node node = req.getNode();
        LocalDateTime now = LocalDateTime.now();
        node.setUpDate(now);
        node.setStatus(Const.STATUS_UPDATED);
        node.setTaskId(req.getTaskId());
        node.setDatasource(commonService.datasource(req.getDatasource(), node.getDatasource()));
        boolean updated = this.updateById(node);
        OlvUtil.check(updated, Const.NODE);
        return node;
    }

    public List<CrossingRenderRes> renderCrossing(String extent) {
        List<CrossingRenderRes> result = new ArrayList<>();
        List<Node> nodes = this.queryByExtent(extent, Const.DEFAULT_QUERY_STATUS);
        String mainnodeid;
        String subnodeid;
        List<String> subnodeidList;
        List<String> nodeidList;
        List<Node> crossingNodeList;
        MultiPoint mPoint;
        List<Point> pointList;
        for (Node node : nodes) {
            nodeidList = new ArrayList<>();
            mainnodeid = node.getMainnodeid();
            subnodeid = node.getSubnodeid();
            if (StrUtil.isEmpty(mainnodeid)) continue;
            if (StrUtil.isEmpty(subnodeid)) continue;
            subnodeidList = (Arrays.stream(subnodeid.split("\\|")).map(String::trim).collect(Collectors.toList()));
            nodeidList.add(mainnodeid);
            nodeidList.addAll(subnodeidList);
            crossingNodeList = this.lambdaQuery().in(Node::getHllNodeid, nodeidList).ne(Node::getStatus, Const.STATUS_DELETED).list();
            pointList = new ArrayList<>();
            for (Node crossingNode : crossingNodeList) {
                pointList.add((Point) crossingNode.getGeometry());
            }
            mPoint = geometryFactory.createMultiPoint(pointList.toArray(new Point[0]));

            CrossingRenderRes res = new CrossingRenderRes();
            res.setRender(createCrossPolygon(mPoint));
            res.setMainNodeid(mainnodeid);
            res.setSubNodeids(subnodeidList);
            result.add(res);
        }
        return result;
    }

    private String createCrossPolygon(Geometry mPoint) {
        Point center = mPoint.getCentroid();
        Coordinate[] coordinates = mPoint.getCoordinates();
        Point point;
        DistanceOp distanceOp;
        double distance;
        double maxDistance = 0d;
        for (Coordinate coordinate : coordinates) {
            point = geometryFactory.createPoint(coordinate);
            distanceOp = new DistanceOp(center, point);
            distance = distanceOp.distance();
            if (distance > maxDistance) {
                maxDistance = distance;
            }
        }
        return center.buffer(maxDistance + 0.00005).toText();
    }

    public Map<String, Node> queryMapByNodeids(List<String> hllNodeids) {
        List<List<String>> partitions = Lists.partition(hllNodeids, 2000);
        Map<String, Node> nodeMap = new HashMap<>();
        List<Node> nodes;
        for (List<String> partition : partitions) {
            nodes = this.lambdaQuery().in(Node::getHllNodeid, partition).list();
            nodes.forEach(node -> nodeMap.put(node.getHllNodeid(), node));
        }
        return nodeMap;
    }

    public Node check(String hllNodeid) {
        QueryWrapper<Node> wrapper = new QueryWrapper<>();
        wrapper.eq("hll_nodeid", hllNodeid);
        wrapper.ne("status", Const.STATUS_DELETED);
        Node node = this.getBaseMapper().selectOne(wrapper);
        OlvUtil.check(node != null, Const.NODE);

        Long olv = node.getOlv();

        UpdateWrapper<Node> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("hll_nodeid", hllNodeid);
        updateWrapper.ne("status", Const.STATUS_DELETED);
        updateWrapper.eq("olv", olv);
        updateWrapper.set("olv", olv);
        boolean update = this.update(null, updateWrapper);
        OlvUtil.check(update, Const.NODE);

        return node;
    }

    @DSTransactional
    public CrossingOperateRes createCrossing(CrossingOperateReq req) {
        List<Node> subNodes = req.getSubNodes();
        Node mainNode = req.getMainNode();
        String datasource = req.getDatasource();
        String taskId = req.getTaskId();

        List<Link> crossingLinks = null;
        // 是否复合路口
        boolean compositeIntersection = CollUtil.isNotEmpty(subNodes);
        if (compositeIntersection) {
            crossingLinks = findCrossingLink(req);
            // 校验
            if (CollUtil.isEmpty(crossingLinks)) throw new BizException("Can't find the intersecting roads");
        }
        String mainNodeid = mainNode.getHllNodeid();
        List<String> subNodeids = subNodes.stream().map(Node::getHllNodeid).collect(Collectors.toList());
        LocalDateTime now = LocalDateTime.now();

        // 更新子点
        CrossingOperateRes res = new CrossingOperateRes();
        boolean updated;
        long olv;
        for (Node subNode : subNodes) {
            olv = subNode.getOlv();
            updated = this.lambdaUpdate()
                    .eq(Node::getHllNodeid, subNode.getHllNodeid())
                    .eq(Node::getOlv, olv)
                    .set(Node::getMainnodeid, mainNodeid)
                    .set(Node::getType, Const.NODE_TYPE_CROSSING_SUB_NODE)
                    .set(Node::getStatus, Const.STATUS_UPDATED)
                    .set(Node::getUpDate, now)
                    .set(Node::getDatasource, commonService.datasource(datasource, subNode.getDatasource()))
                    .set(Node::getTaskId, taskId)
                    .set(Node::getOlv, olv + 1)
                    .update();

            OlvUtil.check(updated, Const.NODE);
            res.getSubNodes().add(subNode);
        }

        // 更新主点
        olv = mainNode.getOlv();
        LambdaUpdateChainWrapper<Node> lambdaUpdate = this.lambdaUpdate()
                .eq(Node::getHllNodeid, mainNode.getHllNodeid())
                .eq(Node::getOlv, olv)
                .set(Node::getStatus, Const.STATUS_UPDATED)
                .set(Node::getUpDate, now)
                .set(Node::getDatasource, commonService.datasource(datasource, mainNode.getDatasource()))
                .set(Node::getTaskId, taskId)
                .set(Node::getOlv, olv + 1);

        if (compositeIntersection) {
            lambdaUpdate.set(Node::getMainnodeid, mainNodeid);
            lambdaUpdate.set(Node::getType, Const.NODE_TYPE_CROSSING_MAIN_NODE);
            lambdaUpdate.set(Node::getSubnodeid, StrUtil.join(Const.SYMBOL_PIPE, subNodeids));
        } else {
            lambdaUpdate.set(Node::getType, Const.NODE_TYPE_CROSSING_SINGLE);
            lambdaUpdate.set(Node::getMainnodeid, null);
            lambdaUpdate.set(Node::getSubnodeid, null);
        }

        updated = lambdaUpdate.update();
        OlvUtil.check(updated, Const.NODE);
        res.setMainNode(mainNode);

        // 复合路口才会处理路口link
        if (compositeIntersection) createCrossingLink(crossingLinks, now, datasource, taskId);

        return res;
    }

    @DSTransactional
    public CrossingOperateRes updateCrossing(CrossingOperateReq req) {
        Node reqMainNode = req.getMainNode();

        Node mainNode = this.getById(reqMainNode.getHllNodeid());
        List<String> subNodeids = Arrays.stream(mainNode.getSubnodeid().split(Const.SYMBOL_PIPE_REGEX)).collect(Collectors.toList());
        List<Node> subNodes = this.lambdaQuery().in(Node::getHllNodeid, subNodeids).ne(Node::getStatus, Const.STATUS_DELETED).list();

        CrossingOperateReq crossingDestroyReq = new CrossingOperateReq();
        crossingDestroyReq.setMainNode(mainNode);
        crossingDestroyReq.setSubNodes(subNodes);
        crossingDestroyReq.setTaskId(req.getTaskId());
        crossingDestroyReq.setDatasource(req.getDatasource());
        destroyCrossing(crossingDestroyReq);

        reqMainNode.setOlv(reqMainNode.getOlv() + 1);
        List<Node> reqSubNodes = req.getSubNodes();
        reqSubNodes.stream().filter(subNode -> subNodeids.contains(subNode.getHllNodeid()))
                .forEach(subNode -> subNode.setOlv(subNode.getOlv() + 1));

        CrossingOperateReq crossingCreateReq = new CrossingOperateReq();
        crossingCreateReq.setMainNode(reqMainNode);
        crossingCreateReq.setSubNodes(reqSubNodes);
        crossingCreateReq.setTaskId(req.getTaskId());
        crossingCreateReq.setDatasource(req.getDatasource());

        return createCrossing(crossingCreateReq);
    }

    @DSTransactional
    public CrossingOperateRes destroyCrossing(CrossingOperateReq req) {
        String datasource = req.getDatasource();
        String taskId = req.getTaskId();
        List<Node> subNodes = req.getSubNodes();
        LocalDateTime now = LocalDateTime.now();

        CrossingOperateRes res = new CrossingOperateRes();
        boolean updated;
        long olv;
        for (Node subNode : subNodes) {
            olv = subNode.getOlv();
            updated = this.lambdaUpdate()
                    .eq(Node::getHllNodeid, subNode.getHllNodeid())
                    .eq(Node::getOlv, olv)
                    .set(Node::getMainnodeid, null)
                    .set(Node::getSubnodeid, null)
                    .set(Node::getUpDate, now)
                    .set(Node::getStatus, Const.STATUS_UPDATED)
                    .set(Node::getType, Const.NODE_TYPE_DEFAULT)
                    .set(Node::getLight, Const.NODE_LIGHT_DEFAULT)
                    .set(Node::getDatasource, commonService.datasource(datasource, subNode.getDatasource()))
                    .set(Node::getTaskId, taskId)
                    .set(Node::getOlv, olv + 1)
                    .update();
            OlvUtil.check(updated, Const.NODE);

            res.getSubNodes().add(subNode);
        }

        Node mainNode = req.getMainNode();
        olv = mainNode.getOlv();
        updated = this.lambdaUpdate()
                .eq(Node::getHllNodeid, mainNode.getHllNodeid())
                .eq(Node::getOlv, olv)
                .set(Node::getUpDate, now)
                .set(Node::getStatus, Const.STATUS_UPDATED)
                .set(Node::getType, Const.NODE_TYPE_DEFAULT)
                .set(Node::getLight, Const.NODE_LIGHT_DEFAULT)
                .set(Node::getMainnodeid, null)
                .set(Node::getSubnodeid, null)
                .set(Node::getDatasource, commonService.datasource(datasource, mainNode.getDatasource()))
                .set(Node::getTaskId, taskId)
                .set(Node::getOlv, olv + 1)
                .update();

        OlvUtil.check(updated, Const.NODE);
        res.setMainNode(mainNode);

        // 是否复合路口
        boolean compositeIntersection = CollUtil.isNotEmpty(subNodes);
        if (compositeIntersection) {
            List<Link> crossingLinks = findCrossingLink(req);
            destroyCrossingLink(crossingLinks, now, datasource, taskId);
        }

        return res;
    }

    private void destroyCrossingLink(List<Link> crossingLinks, LocalDateTime now, String datasource, String taskId) {
        boolean updated;
        List<String> formways;
        for (Link link : crossingLinks) {
            formways = Arrays.asList(link.getFormway().split(Const.SYMBOL_COMMA));
            if (formways.contains(Const.LINK_FORMWAY_CROSSING)) {
                if (formways.size() == 1 && formways.get(0).equals(Const.LINK_FORMWAY_CROSSING)) {
                    formways = new ArrayList<>();
                    formways.add(Const.LINK_FORMWAY_NONE);
                } else {
                    formways = formways.stream().filter(s -> !s.equals(Const.LINK_FORMWAY_CROSSING)).collect(Collectors.toList());
                }
                link.setFormway(String.join(Const.SYMBOL_COMMA, formways));
                link.setStatus(Const.STATUS_UPDATED);
                link.setUpDate(now);
                link.setDatasource(commonService.datasource(datasource, link.getDatasource()));
                link.setTaskId(taskId);

                updated = this.linkService.updateById(link);
                OlvUtil.check(updated, Const.LINK);
            }
        }
    }

    private List<Link> findCrossingLink(CrossingOperateReq req) {
        // 找出路口内的所有link
        List<Node> nodes = CollUtil.newArrayList(req.getMainNode());
        if (CollUtil.isNotEmpty(req.getSubNodes())) nodes.addAll(req.getSubNodes());
        // 创建封闭区域
        GeometryFactory geometryFactory = new GeometryFactory();
        List<Coordinate> coordinates = new ArrayList<>();
        for (Node node : nodes) {
            coordinates.add(node.getGeometry().getCoordinate());
        }

        Coordinate[] coordinatesArray = coordinates.toArray(new Coordinate[0]);

        String wkt;
        double buffer = 0.00001;
        if (coordinatesArray.length >= 3) {
            MultiPoint multiPoint = geometryFactory.createMultiPoint(coordinatesArray);
            Geometry polygon = multiPoint.convexHull();
            wkt = polygon.toText();
        } else {
            LineString lineString = geometryFactory.createLineString(coordinatesArray);
            wkt = lineString.toText();
            buffer = 0.00009;
        }

        return this.linkService.lambdaQuery().ne(Link::getStatus, Const.STATUS_DELETED)
                .apply("st_covers(st_buffer(st_geomfromtext({0}, 4326), " + buffer + "), geometry)", wkt)
                .list();
    }

    private void createCrossingLink(List<Link> crossingLinks, LocalDateTime now, String datasource, String taskId) {
        List<String> formways;
        boolean updated;
        for (Link link : crossingLinks) {
            formways = new ArrayList<>(Arrays.asList(link.getFormway().split(Const.SYMBOL_COMMA)));
            if (!formways.contains(Const.LINK_FORMWAY_CROSSING)) {
                if (formways.size() == 1 && formways.contains(Const.LINK_FORMWAY_NONE)) {
                    formways = new ArrayList<>();
                }
                formways.add(Const.LINK_FORMWAY_CROSSING);
                link.setFormway(String.join(Const.SYMBOL_COMMA, formways));
            }
            link.setUpDate(now);
            link.setStatus(Const.STATUS_UPDATED);
            link.setDatasource(commonService.datasource(datasource, link.getDatasource()));
            link.setTaskId(taskId);

            updated = linkService.updateById(link);
            OlvUtil.check(updated, Const.LINK);
        }
    }
}
