package work.llm.map.service.node.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "路口渲染返回体")
public class CrossingRenderRes {

    @Schema(description = "主节点id")
    private String mainNodeid;
    @Schema(description = "子节点ids")
    private List<String> subNodeids;
    @Schema(description = "渲染结果")
    private String render;
}
