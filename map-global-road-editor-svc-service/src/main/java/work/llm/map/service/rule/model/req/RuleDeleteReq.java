package work.llm.map.service.rule.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import work.llm.map.dao.rule.entity.Rule;
import work.llm.map.service.common.DatasourceAndTaskIdReq;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "道路交限删除请求")
public class RuleDeleteReq extends DatasourceAndTaskIdReq {

    @Schema(description = "待删除的道路交限")
    private List<Rule> rules;
}