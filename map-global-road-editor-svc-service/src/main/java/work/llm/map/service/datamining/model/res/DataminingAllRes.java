package work.llm.map.service.datamining.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.vividsolutions.jts.geom.Geometry;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import work.llm.map.common.jackson.deserializer.StringToGeometryDeserializer;
import work.llm.map.common.jackson.serializer.GeometryToStringSerializer;
import work.llm.map.dao.datamining.entity.DataminingGroup;

@Data
@Schema(description = "属性资料全部数据响应类")
public class DataminingAllRes {

    public DataminingAllRes(DataminingGroup group) {
        BeanUtils.copyProperties(group, this);
        this.geometry = group.getDgs().get(0).getGeometry();
    }

    @Schema(description = "资料ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long groupId;
    @Schema(description = "查看标识 0-未查看 1-已查看")
    private Integer editFlag;
    @JsonProperty("geom")
    @Schema(description = "坐标")
    @JsonSerialize(using = GeometryToStringSerializer.class)
    @JsonDeserialize(using = StringToGeometryDeserializer.class)
    private Geometry geometry;
}
