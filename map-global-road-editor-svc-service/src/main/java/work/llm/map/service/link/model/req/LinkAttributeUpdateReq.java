package work.llm.map.service.link.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import work.llm.map.dao.link.entity.Link;
import work.llm.map.service.common.DatasourceAndTaskIdReq;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "道路属性更新请求")
public class LinkAttributeUpdateReq extends DatasourceAndTaskIdReq {

    @Schema(description = "道路属性")
    private Link link;
}
