package work.llm.map.service.task.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "任务预提交请求类")
public class TaskPrecommitReq {

    @Schema(description = "任务ID")
    private String taskId;
    @Schema(description = "1-开始作业 2-作业提交 3-开始质检 4-质检提交 5-质检返工 6-质检返修 7-开始验收 8-验收提交 9-验收返工")
    private Integer commitType;
    @Schema(description = "抽取比例")
    private String extractRatio;
}
