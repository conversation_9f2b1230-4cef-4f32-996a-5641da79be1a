package work.llm.map.service.datamining;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import work.llm.map.dao.datamining.entity.DataminingLine;
import work.llm.map.dao.datamining.mapper.DataminingLineMapper;

import java.util.List;

@Service
public class DataminingLineService extends ServiceImpl<DataminingLineMapper, DataminingLine> {

    public List<DataminingLine> queryByExtent(String extent, List<Long> groupIds) {
        return this.lambdaQuery().in(DataminingLine::getGroupId, groupIds)
                .apply(StrUtil.isNotBlank(extent), "ST_Intersects(ST_GeomFromText({0}, 4326), geometry)", extent).list();
    }
}
