package work.llm.map.service.user.impl;

import work.llm.map.common.utils.MapperUtils;
import work.llm.map.dao.user.UserInfoDao;
import work.llm.map.dao.user.entity.UserInfoPO;
import work.llm.map.service.user.UserInfoService;
import work.llm.map.service.user.model.UserInfoDO;
import com.github.pagehelper.PageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/1
 */
@Service
public class UserInfoServiceImpl implements UserInfoService {

	@Resource
	private UserInfoDao userInfoDao;

	/**
	 * 获取用户信息
	 *
	 * @param id id
	 * @return {@link UserInfoDO}
	 */
	@Override
	public UserInfoDO getById(Long id) {
		UserInfoPO userInfoPo = userInfoDao.getById(id);
		return MapperUtils.map(userInfoPo, UserInfoDO.class);
	}

	public List<UserInfoDO> getPageList(int page, int pageSize){
		List<UserInfoPO> userInfoList = userInfoDao.queryPageList(page, pageSize);
		return userInfoList.stream().map(userInfoPo -> MapperUtils.map(userInfoPo, UserInfoDO.class)).collect(Collectors.toList());
	}

	public Integer getPageCount(){
		return userInfoDao.queryPageCount();
	}
}
