package work.llm.map.service.datamining;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import work.llm.map.dao.datamining.entity.DataminingPoint;
import work.llm.map.dao.datamining.mapper.DataminingPointMapper;

import java.util.List;

@Service
public class DataminingPointService extends ServiceImpl<DataminingPointMapper, DataminingPoint> {

    public List<DataminingPoint> queryByExtent(String extent, List<Long> groupIds) {
        return this.lambdaQuery().in(DataminingPoint::getGroupId, groupIds)
                .apply(StrUtil.isNotBlank(extent), "ST_Intersects(ST_GeomFromText({0}, 4326), geometry)", extent).list();
    }
}
