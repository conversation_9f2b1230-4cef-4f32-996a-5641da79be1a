package work.llm.map.service.scheduler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.StrUtil;
import cn.lalaframework.dynamic.datasource.annotation.DSTransactional;
import cn.lalaframework.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import cn.lalaframework.job.sdk.HllXxlJobManager;
import cn.lalaframework.job.sdk.annotation.HllXxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import work.llm.map.common.constant.Const;
import work.llm.map.common.utils.JsonUtils;
import work.llm.map.dao.link.entity.Link;
import work.llm.map.dao.rule.entity.Rule;
import work.llm.map.service.common.CommonService;
import work.llm.map.service.common.PathCalculator;
import work.llm.map.service.link.LinkService;
import work.llm.map.service.rule.RuleService;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2025/5/28
 */
@Slf4j
@Component
@SuppressWarnings("unchecked")
public class RuleInfoJobHandler {
    @Resource
    private RuleService ruleService;
    @Resource
    private CommonService commonService;
    @Resource
    private LinkService linkService;

    @HllXxlJob(value = "ruleInfoJobHandler")
    public void execute() throws Exception {
        HllXxlJobManager.log("XXL-JOB, starting RuleInfoJobHandler...");
        log.info("XXL-JOB, starting RuleInfoJobHandler...");
        TimeInterval timer = DateUtil.timer();
        HllXxlJobManager.log("RuleInfoJobHandler started, start time: {} ", DateUtil.now());
        log.info("RuleInfoJobHandler started, start time: {} ", DateUtil.now());
        // 获取执行参数
        String command = HllXxlJobManager.getJobParam();
        Map<String, String> parameters = JsonUtils.parseJson(command, Map.class);
        HllXxlJobManager.log("ruleInfoJobHandler's parameters: " + parameters);
        // 业务逻辑
        try {
            String[] schemas = parameters.get("schemas").split(Const.SYMBOL_COMMA);
            for (String schema : schemas) {
                String old = DynamicDataSourceContextHolder.peek();
                DynamicDataSourceContextHolder.push(schema);
                try {
                    doHandleRuleInfo();
                } finally {
                    DynamicDataSourceContextHolder.push(old);
                }
            }
            HllXxlJobManager.log("RuleInfoJobHandler completed, end time: {} , cost time : {} ms", DateUtil.now(), timer.intervalMs());
            log.info("RuleInfoJobHandler completed, end time: {} , cost time : {} ms", DateUtil.now(), timer.intervalMs());
        } catch (Exception e) {
            log.error("RuleInfoJobHandler error", e);
            HllXxlJobManager.log(e.getMessage(), e);
            // 如果出现该异常表示任务执行失败的话可通过如下方法标记任务执行为失败状态
            HllXxlJobManager.handleFail(e.getMessage());
        }
    }

    @DSTransactional
    private void doHandleRuleInfo() {
        long handleNum = 0L;

        while (true) {
            // 只查 rule_info 为空且未删除的数据
            List<Rule> rules = ruleService.lambdaQuery()
                    .ne(Rule::getStatus, Const.STATUS_DELETED)
                    .isNull(Rule::getRuleInfo).list();

            if (CollUtil.isEmpty(rules)) {
                break;
            }

            // 计算 ruleInfo
            rules.forEach(this::fillRuleInfo);

            // 分页更新，降低锁粒度
            // if (ruleService.updateBatchById(rules, PAGE_SIZE)) {
            //     handleNum += rules.size();
            // }
            // 使用 LambdaUpdateWrapper 确保更新 null 值
            for (Rule rule : rules) {
                boolean updated = ruleService.lambdaUpdate()
                        .eq(Rule::getRuleId, rule.getRuleId())
                        .set(Rule::getRuleInfo, rule.getRuleInfo())
                        .update();

                if (updated) {
                    handleNum++;
                } else {
                    log.warn("Failed to update rule: {}", rule.getRuleId());
                }
            }
        }
        log.info("current market={}, handle rule(rule_info) size={}",
                DynamicDataSourceContextHolder.peek(), handleNum);
    }

    /**
     * 根据 inlink/outlink 计算并设置 ruleInfo
     */
    private void fillRuleInfo(Rule rule) {
        log.info("fill rule info, ruleId={}", rule.getRuleId());
        String inId = rule.getInlinkId();
        String outId = rule.getOutlinkId();

        // 车道掉头
        if (Objects.equals(inId, outId)) {
            rule.setRuleInfo(Const.RULE_INFO_TURN);
            return;
        }

        // 其余场景
        PathCalculator calculator = new PathCalculator(inId, outId);
        List<String> pathIds = calculator.ruleComplete();
        Map<String, Link> linkMap = calculator.getLinkMap();

        if (CollUtil.isEmpty(pathIds)) {
            Link inLink = linkService.queryById(inId, Const.DEFAULT_QUERY_STATUS);
            Link outLink = linkService.queryById(outId, Const.DEFAULT_QUERY_STATUS);
            if (!CollUtil.intersection(CollUtil.newArrayList(inLink.getHllSNid(), inLink.getHllENid()), CollUtil.newArrayList(outLink.getHllSNid(), outLink.getHllENid())).isEmpty()) {
                pathIds = CollUtil.newArrayList(inId, outId);
            } else {
                rule.setRuleInfo(Const.RULE_INFO_UNKNOWN);
                return;
            }
        }

        Link inLink = linkMap.get(inId);
        Link inLinkNext = linkMap.get(pathIds.get(1));
        Link outLink = linkMap.get(outId);
        Link outLinkBefore = linkMap.get(pathIds.get(pathIds.size() - 2));

        int ruleInfo = commonService.ruleInfo(inLink, outLink, inLinkNext, outLinkBefore);
        if (rule.getFlag() == 3 && ruleInfo == Const.RULE_INFO_TURN) {
            rule.setRuleInfo(Const.RULE_INFO_ALLOW_TURN);
        } else {
            rule.setRuleInfo(ruleInfo);
        }
    }
}
