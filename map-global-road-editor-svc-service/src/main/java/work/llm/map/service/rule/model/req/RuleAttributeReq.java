package work.llm.map.service.rule.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import work.llm.map.dao.rule.entity.Rule;
import work.llm.map.service.common.DatasourceAndTaskIdReq;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "道路交限属性请求")
public class RuleAttributeReq extends DatasourceAndTaskIdReq {

    @Schema(description = "交限属性")
    private Rule rule;
}
