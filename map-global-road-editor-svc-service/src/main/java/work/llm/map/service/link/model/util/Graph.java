package work.llm.map.service.link.model.util;

import work.llm.map.dao.link.entity.Link;

import java.util.*;

public class Graph {
    private final Map<String, List<String>> adjList; // 邻接表

    private Graph() {
        adjList = new HashMap<>();
    }

    // 根据路线模型构建图
    private void addLinkModel(Link model) {
        adjList.computeIfAbsent(model.getHllSNid(), x -> new ArrayList<>()).add(model.getHllENid());
        adjList.computeIfAbsent(model.getHllENid(), x -> new ArrayList<>()).add(model.getHllSNid()); // 如果是无向图
    }

    // 使用DFS来检查图是否连通
    private boolean isConnected() {
        Set<String> visited = new HashSet<>();
        String startNode = adjList.keySet().iterator().next(); // 随便选择一个起始点
        dfs(startNode, visited);
        return visited.size() == adjList.size(); // 如果访问的节点数等于图的节点数，则连通
    }

    private void dfs(String node, Set<String> visited) {
        visited.add(node);
        for (String neighbor : adjList.getOrDefault(node, Collections.emptyList())) {
            if (!visited.contains(neighbor)) {
                dfs(neighbor, visited);
            }
        }
    }

    public static boolean connected(List<Link> links) {
        Graph graph = new Graph();
        for (Link link : links) {
            graph.addLinkModel(link);
        }
        return graph.isConnected();
    }
}
