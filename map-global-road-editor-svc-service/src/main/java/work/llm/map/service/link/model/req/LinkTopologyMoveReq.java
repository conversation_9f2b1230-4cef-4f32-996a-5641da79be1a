package work.llm.map.service.link.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import work.llm.map.dao.link.entity.Link;
import work.llm.map.dao.node.entity.Node;
import work.llm.map.service.common.DatasourceAndTaskIdReq;

import java.util.List;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "道路拓扑联动请求")
public class LinkTopologyMoveReq extends DatasourceAndTaskIdReq {

    @Schema(description = "被移动道路信息")
    private List<Link> moveLinks;
    @Schema(description = "移动点信息")
    private Node moveNode;
}
