package work.llm.map.service.link;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.lalaframework.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.vividsolutions.jts.geom.Coordinate;
import com.vividsolutions.jts.geom.Geometry;
import com.vividsolutions.jts.geom.GeometryFactory;
import com.vividsolutions.jts.geom.Point;
import com.vividsolutions.jts.linearref.LinearLocation;
import com.vividsolutions.jts.linearref.LocationIndexedLine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import work.llm.map.common.constant.Const;
import work.llm.map.common.exception.BizException;
import work.llm.map.common.utils.DistanceUtil;
import work.llm.map.common.utils.OlvUtil;
import work.llm.map.common.utils.StatusUtil;
import work.llm.map.dao.link.entity.Link;
import work.llm.map.dao.link.mapper.LinkMapper;
import work.llm.map.dao.matlink.entity.MatLink;
import work.llm.map.dao.node.entity.Node;
import work.llm.map.service.common.CommonService;
import work.llm.map.service.link.model.req.*;
import work.llm.map.service.link.model.util.Graph;
import work.llm.map.service.matlink.MatLinkService;
import work.llm.map.service.node.NodeService;
import work.llm.map.service.node.model.template.NodeTemplate;
import work.llm.map.service.relation.RelationService;
import work.llm.map.service.rule.RuleService;
import work.llm.map.service.zlevel.ZlevelService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@SuppressWarnings("Duplicates")
public class LinkService extends ServiceImpl<LinkMapper, Link> {

    private final GeometryFactory geometryFactory = new GeometryFactory();

    @Resource
    private CommonService commonService;
    @Resource
    private NodeService nodeService;
    @Resource
    private RuleService ruleService;
    @Resource
    private RelationService relationService;
    @Resource
    private MatLinkService matLinkService;
    @Resource
    private ZlevelService zlevelService;

    public List<Link> queryByExtent(String extent, String status) {
        List<Link> links = this.lambdaQuery().in(Link::getStatus, StatusUtil.status(status))
                .apply("ST_Intersects(ST_GeomFromText({0}, 4326), geometry)", extent).list();

        // 找出links里面geometry相同的分组，geometry相同的算法是equals
        return links.stream().peek(first -> {
            if (links.stream()
                    .anyMatch(two -> !two.getHllLinkid().equals(first.getHllLinkid())
                                     && first.getGeometry().equals(two.getGeometry())))
                first.setCovered(Const.YES);
        }).collect(Collectors.toList());
    }

    public Link queryById(String hllLinkid, String status) {
        return this.lambdaQuery().eq(Link::getHllLinkid, hllLinkid).in(Link::getStatus, StatusUtil.status(status)).one();
    }

    @DSTransactional
    public boolean topologySeparate(LinkTopologySeparateReq req) {
        commonService.checkReq(req);
        String datasource = req.getDatasource();
        String taskId = req.getTaskId();

        Node moveNode = req.getMoveNode();
        Link moveLink = req.getMoveLink();
        Geometry moveLinkGeom = moveLink.getGeometry();

        LocalDateTime now = LocalDateTime.now();
        String hllNodeid = String.valueOf(commonService.generateId(datasource));

        moveLink.setGeometry(moveLinkGeom);
        moveLink.setGeomwkt(moveLinkGeom.toText());
        moveLink.setLen(DistanceUtil.distance(moveLinkGeom));
        moveLink.setUpDate(now);
        moveLink.setStatus(Const.STATUS_UPDATED);
        moveLink.setDatasource(commonService.datasource(datasource, moveLink.getDatasource()));
        moveLink.setTaskId(taskId);
        if (moveNode.getHllNodeid().equals(moveLink.getHllSNid())) {
            moveLink.setHllSNid(hllNodeid);
        } else if (moveNode.getHllNodeid().equals(moveLink.getHllENid())) {
            moveLink.setHllENid(hllNodeid);
        } else {
            throw new BizException("Request Parameters Error [moveNode and moveLink are not associated]");
        }

        boolean updated = this.updateById(moveLink);
        OlvUtil.check(updated, Const.LINK);

        Node newNode = new Node();
        BeanUtils.copyProperties(moveNode, newNode);

        newNode.setHllNodeid(hllNodeid);
        newNode.setNodeId(hllNodeid);
        newNode.setKind(Const.DEFAULT_NODE_KIND);
        newNode.setType(Const.NODE_TYPE_DEFAULT);
        newNode.setLight(Const.NODE_LIGHT_DEFAULT);
        newNode.setMainnodeid(null);

        newNode.setUpDate(now);
        newNode.setStatus(Const.STATUS_NEW);
        newNode.setDatasource(datasource);
        newNode.setTaskId(taskId);
        newNode.setGeometry(moveNode.getGeometry());
        newNode.setGeomwkt(moveNode.getGeometry().toText());

        Integer area = getArea(moveLink);
        newNode.setArea(area);

        this.updateConnectionTable(moveLink.getHllLinkid(), taskId, datasource);

        return this.nodeService.save(newNode);
    }

    @DSTransactional
    public boolean topologyMove(LinkTopologyMoveReq req) {
        commonService.checkReq(req);
        List<Link> moveLinks = req.getMoveLinks();
        Node moveNode = req.getMoveNode();

        String taskId = req.getTaskId();
        String datasource = req.getDatasource();
        LocalDateTime now = LocalDateTime.now();

        boolean updated;
        for (Link moveLink : moveLinks) {
            moveLink.setGeomwkt(moveLink.getGeometry().toText());
            moveLink.setLen(DistanceUtil.distance(moveLink.getGeometry()));
            moveLink.setUpDate(now);
            moveLink.setTaskId(taskId);
            moveLink.setStatus(Const.STATUS_UPDATED);
            moveLink.setDatasource(commonService.datasource(datasource, moveLink.getDatasource()));
            updated = this.updateById(moveLink);
            OlvUtil.check(updated, Const.LINK);
        }

        moveNode.setUpDate(now);
        moveNode.setTaskId(taskId);
        moveNode.setStatus(Const.STATUS_UPDATED);
        moveNode.setDatasource(commonService.datasource(datasource, moveNode.getDatasource()));
        moveNode.setGeomwkt(moveNode.getGeometry().toText());
        updated = this.nodeService.updateById(moveNode);
        OlvUtil.check(updated, Const.NODE);

        return true;
    }

    @DSTransactional
    public Link updateAttribute(LinkAttributeUpdateReq req) {
        commonService.checkReq(req);
        Link link = req.getLink();
        LocalDateTime now = LocalDateTime.now();
        link.setUpDate(now);
        link.setStatus(Const.STATUS_UPDATED);
        link.setTaskId(req.getTaskId());
        link.setDatasource(commonService.datasource(req.getDatasource(), link.getDatasource()));
        boolean updated = this.updateById(link);
        OlvUtil.check(updated, Const.LINK);
        return link;
    }

    @DSTransactional
    public boolean batchDelete(LinkDeleteBatchReq req) {
        commonService.checkReq(req);
        List<Link> reqLinks = req.getLinks();
        String datasource = req.getDatasource();
        String taskId = req.getTaskId();
        if (CollUtil.isEmpty(reqLinks)) throw new BizException("Parameter Error: The 'links' field cannot be empty.");
        else if (reqLinks.size() > 10)
            throw new BizException("Parameter Error: The number of 'links' cannot exceed 10.");

        List<String> formways = reqLinks.stream().map(Link::getFormway).collect(Collectors.toList());
        if (formways.contains("50"))
            throw new BizException("Parameter Error: 'formway' cannot contain the value 50. Please edit the intersection and try again.");
        if (!Graph.connected(reqLinks))
            throw new BizException("Parameter Error: 'Links' are not connected; deletion across disconnected links is not supported.");

        LocalDateTime now = LocalDateTime.now();
        boolean updated;

        // 删除node
        List<String> nodeIds = connectedNodes(reqLinks);
        List<Node> nodes = new ArrayList<>();
        if (CollUtil.isNotEmpty(nodeIds)) nodes = this.nodeService.lambdaQuery().in(Node::getHllNodeid, nodeIds).list();

        for (Node node : nodes) {
            node.setUpDate(now);
            node.setStatus(Const.STATUS_DELETED);
            node.setTaskId(taskId);
            node.setDatasource(commonService.datasource(datasource, node.getDatasource()));
            updated = this.nodeService.updateById(node);
            OlvUtil.check(updated, Const.NODE);
        }

        // 删除link
        List<String> linkIds = reqLinks.stream().map(Link::getHllLinkid).collect(Collectors.toList());
        List<Link> links = this.lambdaQuery().in(Link::getHllLinkid, linkIds).list();
        for (Link link : links) {
            link.setUpDate(now);
            link.setStatus(Const.STATUS_DELETED);
            link.setTaskId(taskId);
            link.setDatasource(commonService.datasource(datasource, link.getDatasource()));
            updated = this.updateById(link);
            OlvUtil.check(updated, Const.LINK);
        }

        // 删除关联表
        for (String linkId : linkIds)
            deleteConnectionTable(linkId, taskId, datasource);
        return true;
    }

    @DSTransactional
    public boolean connectNode(LinkMaterialConnectReq req) {
        commonService.checkReq(req);
        preHandle(req.getMatLink());
        Link link = req.getLink();
        String taskId = req.getTaskId();
        String datasource = req.getDatasource();
        MatLink matLink = req.getMatLink();

        LocalDateTime now = LocalDateTime.now();
        // 资料线坐标
        Geometry sourceGeom = matLink.getGeometry();
        // 挂接线坐标
        Geometry targetGeom = link.getGeometry();

        Point targetFirstPoint = geometryFactory.createPoint(targetGeom.getCoordinates()[0]);
        Point targetLastPoint = geometryFactory.createPoint(targetGeom.getCoordinates()[targetGeom.getCoordinates().length - 1]);
        Point sourceFirstPoint = geometryFactory.createPoint(sourceGeom.getCoordinates()[0]);
        Point sourceLastPoint = geometryFactory.createPoint(sourceGeom.getCoordinates()[sourceGeom.getCoordinates().length - 1]);
        if (buffer(sourceFirstPoint).intersects(targetGeom) && buffer(sourceLastPoint).intersects(targetGeom)) {
            throw new BizException("Failed to attach point: Both ends of the data line intersect with the target line.");
        }
        if (buffer(targetFirstPoint).intersects(sourceGeom) && buffer(targetLastPoint).intersects(sourceGeom)) {
            throw new BizException("Failed to attach point: Both ends of the target line intersect with the data line.");
        }
        int length = sourceGeom.intersection(targetGeom).getCoordinates().length;
        if (length > 1) {
            throw new BizException("Failed to attach point: The data line and target line have non-unique intersection points (" + length + ").");
        }

        String hllSNid = link.getHllSNid();
        String hllENid = link.getHllENid();
        List<Long> ids = this.commonService.generateIds(datasource, 3);

        Link newLink = new Link();
        BeanUtils.copyProperties(matLink, newLink);

        // 如果资料的车辆类型为空，则使用被挂接的车辆类型
        if (StrUtil.isBlank(newLink.getArVeh())) newLink.setArVeh(link.getArVeh());

        newLink.setTimeZone(link.getTimeZone());
        newLink.setTAdmin(link.getTAdmin());
        newLink.setLen(DistanceUtil.distance(newLink.getGeometry()));
        newLink.setStatus(Const.STATUS_NEW);
        newLink.setUpDate(now);
        newLink.setTaskId(taskId);
        newLink.setDatasource(datasource);
        newLink.setGeomwkt(newLink.getGeometry().toText());
        if (StrUtil.isBlank(newLink.getLAdmin())) {
            newLink.setLAdmin(link.getLAdmin());
        }
        if (StrUtil.isBlank(newLink.getRAdmin())) {
            newLink.setRAdmin(link.getRAdmin());
        }
        if (StrUtil.isBlank(matLink.getTileId())) {
            newLink.setTileId(commonService.getTileId(matLink.getGeometry().toText()));
        }
        if (Objects.isNull(matLink.getTileType())) {
            newLink.setTileType(Const.TILE_TYPE_DEFAULT);
        }

        Integer area = getArea(link);

        Node newNode = new Node();
        BeanUtils.copyProperties(new NodeTemplate(), newNode);
        newNode.setStatus(Const.STATUS_NEW);
        newNode.setUpDate(now);
        newNode.setTaskId(taskId);
        newNode.setDatasource(datasource);
        newNode.setArea(area);

        // 这里能够确定相交点位
        String newNodeid;
        if (buffer(sourceFirstPoint).intersects(targetGeom)) {
            if (buffer(targetFirstPoint).intersects(sourceGeom)) {
                newLink.setHllSNid(hllSNid);
                newNodeid = matLink.getHllENid();
            } else {
                newLink.setHllSNid(hllENid);
                newNodeid = matLink.getHllSNid();
            }
            if (needNewNodeid(newNodeid)) {
                newNodeid = String.valueOf(ids.get(0));
            }
            newLink.setHllENid(newNodeid);
            newNode.setGeometry(sourceLastPoint);
            newNode.setGeomwkt(sourceLastPoint.toText());
        } else {
            if (buffer(targetFirstPoint).intersects(sourceGeom)) {
                newLink.setHllENid(hllSNid);
                newNodeid = matLink.getHllENid();
            } else {
                newLink.setHllENid(hllENid);
                newNodeid = matLink.getHllSNid();
            }
            if (needNewNodeid(newNodeid)) {
                newNodeid = String.valueOf(ids.get(0));
            }
            newLink.setHllSNid(newNodeid);
            newNode.setGeometry(sourceFirstPoint);
            newNode.setGeomwkt(sourceFirstPoint.toText());
        }

        newNode.setHllNodeid(newNodeid);
        newNode.setNodeId(newNodeid);
        if (StrUtil.isBlank(newNode.getTileId())) {
            newNode.setTileId(commonService.getTileId(matLink.getGeometry().toText()));
            newNode.setTileType(Const.TILE_TYPE_DEFAULT);
        }

        // 新增或者更新资料
        String matHllLinkid = matLink.getHllLinkid();
        matLink.setWorkStatus(Const.WORK_STATUS_WORKED);
        // 如果是作业员画的或者link中已经有该资料ID的link
        if (StrUtil.isBlank(matHllLinkid)) {
            BeanUtils.copyProperties(newLink, matLink);

            matHllLinkid = String.valueOf(ids.get(1));
            matLink.setHllLinkid(matHllLinkid);
            matLink.setLinkId(matHllLinkid);
            matLink.setCollectVersion("self");
            matLink.setCreateTime(now);
            matLink.setUpdateTime(now);
            matLink.setWorkStatus(Const.WORK_STATUS_DRAW);
        }
        matLink.setUpdateTime(now);

        // 如果id存在
        String newLinkid = matHllLinkid;
        if (this.lambdaQuery().eq(Link::getHllLinkid, newLinkid).count() > 0) {
            newLinkid = String.valueOf(ids.get(2));
        }

        matLink.setRealLinkid(newLinkid);

        // 判断一下资料是不是存在，存在的话将路形修改成原来的
        MatLink existMatLink = this.matLinkService.getById(matLink.getMatId());
        if (Objects.nonNull(existMatLink)) {
            matLink.setGeometry(existMatLink.getGeometry());
            matLink.setGeomwkt(existMatLink.getGeometry().toText());
        }
        this.matLinkService.saveOrUpdate(matLink);

        newLink.setHllLinkid(newLinkid);
        newLink.setLinkId(newLinkid);
        newLink.setArea(area);
        this.save(newLink);

        this.nodeService.save(newNode);

        return true;
    }

    private Integer getArea(Link link) {
        Integer area;
        if (link.getArea() == null) {
            area = this.lambdaQuery().eq(Link::getHllLinkid, link.getHllLinkid()).one().getArea();
            if (area == null) {
                area = 0;
            }
        } else {
            area = link.getArea();
        }
        return area;
    }

    private void preHandle(MatLink matLink) {
        if (matLink.getWorkStatus().equals(Const.WORK_STATUS_DRAW)) {
            matLink.setHllLinkid(null);
        }
    }

    @DSTransactional
    public boolean connectLink(LinkMaterialConnectReq req) {
        commonService.checkReq(req);
        preHandle(req.getMatLink());
        Link link = req.getLink();
        MatLink matLink = req.getMatLink();
        String hllLinkid = link.getHllLinkid();
        String datasource = req.getDatasource();
        String taskId = req.getTaskId();

        Geometry sourceGeom = matLink.getGeometry();
        Geometry targetGeom = link.getGeometry();

        Geometry intersectionPoint = sourceGeom.intersection(targetGeom);
        int length = intersectionPoint.getCoordinates().length;
        if (length > 1) {
            throw new BizException("Failed to attach point: The data line and target line have non-unique intersection points (" + length + ").");
        }

        Point sourceFirstPoint = geometryFactory.createPoint(sourceGeom.getCoordinates()[0]);
        Point sourceLastPoint = geometryFactory.createPoint(sourceGeom.getCoordinates()[sourceGeom.getCoordinates().length - 1]);
        Point targetFirstPoint = geometryFactory.createPoint(targetGeom.getCoordinates()[0]);

        if (buffer(sourceFirstPoint).intersects(targetGeom)
            && buffer(sourceLastPoint).intersects(targetGeom)) {
            throw new BizException("Failed to attach point: Both endpoints of the data line are too close to the target line");
        }

        List<Long> ids = commonService.generateIds(datasource, 6);
        LocalDateTime now = LocalDateTime.now();

        List<Geometry> splitLineStrings = split(targetGeom, intersectionPoint);
        if (brief(splitLineStrings, targetFirstPoint)) {
            throw new BizException("Failed to attach line: The road segment after splitting is too short.");
        }

        String firstHllLinkid = String.valueOf(ids.get(0));
        Link firstLink = new Link();
        BeanUtils.copyProperties(link, firstLink);
        firstLink.setHllLinkid(firstHllLinkid);
        firstLink.setLinkId(firstHllLinkid);
        firstLink.setStatus(Const.STATUS_NEW);
        firstLink.setDatasource(datasource);
        firstLink.setTaskId(taskId);
        firstLink.setUpDate(now);

        String secondHllLinkid = String.valueOf(ids.get(1));
        Link secondLink = new Link();
        BeanUtils.copyProperties(link, secondLink);
        secondLink.setHllLinkid(secondHllLinkid);
        secondLink.setLinkId(secondHllLinkid);
        secondLink.setStatus(Const.STATUS_NEW);
        secondLink.setDatasource(datasource);
        secondLink.setTaskId(taskId);
        secondLink.setUpDate(now);

        String intersectHllNodeid = String.valueOf(ids.get(2));
        for (Geometry splitLineString : splitLineStrings) {
            if (buffer(targetFirstPoint).intersects(splitLineString)) {
                firstLink.setGeometry(splitLineString);
                firstLink.setGeomwkt(splitLineString.toText());
                firstLink.setHllENid(intersectHllNodeid);
            } else {
                secondLink.setGeometry(splitLineString);
                secondLink.setGeomwkt(splitLineString.toText());
                secondLink.setHllSNid(intersectHllNodeid);
            }
        }
        Integer area = getArea(link);

        firstLink.setLen(DistanceUtil.distance(firstLink.getGeometry()));
        firstLink.setLinkOld(hllLinkid);
        firstLink.setLinkNew(firstLink.getHllLinkid());
        firstLink.setArea(area);
        this.save(firstLink);

        secondLink.setLen(DistanceUtil.distance(secondLink.getGeometry()));
        secondLink.setLinkOld(hllLinkid);
        secondLink.setLinkNew(secondLink.getHllLinkid());
        secondLink.setArea(area);
        this.save(secondLink);

        NodeTemplate nodeTemplate = new NodeTemplate();
        Node intersectNode = new Node();
        BeanUtils.copyProperties(nodeTemplate, intersectNode);
        intersectNode.setHllNodeid(intersectHllNodeid);
        intersectNode.setNodeId(intersectHllNodeid);
        intersectNode.setGeometry(intersectionPoint);
        intersectNode.setGeomwkt(intersectionPoint.toText());
        intersectNode.setUpDate(now);
        intersectNode.setDatasource(datasource);
        intersectNode.setStatus(Const.STATUS_NEW);
        intersectNode.setTaskId(taskId);
        intersectNode.setArea(area);
        if (StrUtil.isBlank(intersectNode.getTileId())) {
            intersectNode.setTileId(commonService.getTileId(intersectionPoint.toText()));
            intersectNode.setTileType(Const.TILE_TYPE_DEFAULT);
        }
        this.nodeService.save(intersectNode);

        Node newNode = new Node();
        BeanUtils.copyProperties(nodeTemplate, newNode);
        newNode.setUpDate(now);
        newNode.setDatasource(datasource);
        newNode.setStatus(Const.STATUS_NEW);
        newNode.setTaskId(taskId);

        Link newLink = new Link();
        BeanUtils.copyProperties(matLink, newLink);

        // 如果资料的车辆类型为空，则使用被挂接的车辆类型
        if (StrUtil.isBlank(newLink.getArVeh())) newLink.setArVeh(link.getArVeh());

        newLink.setTimeZone(link.getTimeZone());
        newLink.setTAdmin(link.getTAdmin());
        if (StrUtil.isBlank(newLink.getLAdmin())) {
            newLink.setLAdmin(link.getLAdmin());
        }
        if (StrUtil.isBlank(newLink.getRAdmin())) {
            newLink.setRAdmin(link.getRAdmin());
        }
        if (StrUtil.isBlank(matLink.getTileId())) {
            newLink.setTileId(commonService.getTileId(matLink.getGeometry().toText()));
        }
        if (Objects.isNull(matLink.getTileType())) {
            newLink.setTileType(Const.TILE_TYPE_DEFAULT);
        }

        newLink.setStatus(Const.STATUS_NEW);
        newLink.setUpDate(now);
        newLink.setGeometry(sourceGeom);
        newLink.setGeomwkt(sourceGeom.toText());
        newLink.setTaskId(taskId);
        newLink.setDatasource(datasource);
        newLink.setLen(DistanceUtil.distance(newLink.getGeometry()));

        String newNodeid;
        if (buffer(sourceFirstPoint).intersects(targetGeom)) {
            intersectNode.setGeometry(sourceFirstPoint);
            intersectNode.setGeomwkt(sourceFirstPoint.toText());
            newNode.setGeometry(sourceLastPoint);
            newNode.setGeomwkt(sourceLastPoint.toText());
            newLink.setHllSNid(intersectHllNodeid);
            newNodeid = matLink.getHllENid();
            if (needNewNodeid(newNodeid)) {
                newNodeid = String.valueOf(ids.get(3));
            }
            newLink.setHllENid(newNodeid);
        } else {
            intersectNode.setGeometry(sourceLastPoint);
            intersectNode.setGeomwkt(sourceLastPoint.toText());
            newNode.setGeometry(sourceFirstPoint);
            newNode.setGeomwkt(sourceFirstPoint.toText());
            newLink.setHllENid(intersectHllNodeid);
            newNodeid = matLink.getHllSNid();
            if (needNewNodeid(newNodeid)) {
                newNodeid = String.valueOf(ids.get(3));
            }
            newLink.setHllSNid(newNodeid);
        }
        newNode.setHllNodeid(newNodeid);
        newNode.setNodeId(newNodeid);
        newNode.setArea(area);
        if (StrUtil.isBlank(newNode.getTileId())) {
            newNode.setTileId(commonService.getTileId(newNode.getGeometry().toText()));
            newNode.setTileType(Const.TILE_TYPE_DEFAULT);
        }
        this.nodeService.save(newNode);

        // 删除被打断的link
        link.setUpDate(now);
        link.setStatus(Const.STATUS_DELETED);
        link.setTaskId(taskId);
        link.setDatasource(commonService.datasource(datasource, link.getDatasource()));
        boolean updated = this.updateById(link);
        OlvUtil.check(updated, Const.LINK);

        // 新增或者更新资料
        String matHllLinkid = matLink.getHllLinkid();
        matLink.setWorkStatus(Const.WORK_STATUS_WORKED);
        if (StrUtil.isBlank(matHllLinkid)) {
            BeanUtils.copyProperties(newLink, matLink);

            matHllLinkid = String.valueOf(ids.get(4));
            matLink.setHllLinkid(matHllLinkid);
            matLink.setLinkId(matHllLinkid);
            matLink.setCollectVersion("self");
            matLink.setCreateTime(now);
            matLink.setUpdateTime(now);
            matLink.setWorkStatus(Const.WORK_STATUS_DRAW);
        }
        matLink.setUpdateTime(now);

        String newLinkid = matHllLinkid;
        if (this.lambdaQuery().eq(Link::getHllLinkid, newLinkid).count() > 0) {
            newLinkid = String.valueOf(ids.get(5));
        }

        matLink.setRealLinkid(newLinkid);
        // 判断一下资料是不是存在，存在的话将路形修改成原来的
        MatLink existMatLink = this.matLinkService.getById(matLink.getMatId());
        if (Objects.nonNull(existMatLink)) {
            matLink.setGeometry(existMatLink.getGeometry());
            matLink.setGeomwkt(existMatLink.getGeometry().toText());
        }
        this.matLinkService.saveOrUpdate(matLink);

        newLink.setHllLinkid(newLinkid);
        newLink.setLinkId(newLinkid);
        newLink.setArea(area);
        this.save(newLink);

        //同步更新其他表
        this.splitConnectionTable(hllLinkid, firstLink, secondLink);
        return true;
    }

    @DSTransactional
    public boolean connectNode(LinkConnectReq req) {
        commonService.checkReq(req);
        String taskId = req.getTaskId();
        String datasource = req.getDatasource();
        LocalDateTime now = LocalDateTime.now();

        Link targetLink = req.getLink();
        Link moveLink = req.getMoveLink();

        Geometry targetGeom = targetLink.getGeometry();
        Geometry moveLinkGeom = moveLink.getGeometry();

        Point targetFirstPoint = geometryFactory.createPoint(targetGeom.getCoordinates()[0]);
        Point targetLastPoint = geometryFactory.createPoint(targetGeom.getCoordinates()[targetGeom.getCoordinates().length - 1]);
        Point movedFirstPoint = geometryFactory.createPoint(moveLinkGeom.getCoordinates()[0]);
        Point movedLastPoint = geometryFactory.createPoint(moveLinkGeom.getCoordinates()[moveLinkGeom.getCoordinates().length - 1]);

        if (buffer(movedFirstPoint).intersects(targetGeom) && buffer(movedLastPoint).intersects(targetGeom)) {
            throw new BizException("Failed to attach point: Both ends of the connecting line intersect with the target line.");
        }

        moveLink.setGeometry(moveLinkGeom);
        moveLink.setGeomwkt(moveLinkGeom.toText());
        moveLink.setUpDate(now);
        moveLink.setDatasource(commonService.datasource(datasource, moveLink.getDatasource()));
        moveLink.setTaskId(taskId);
        moveLink.setStatus(Const.STATUS_UPDATED);
        moveLink.setLen(DistanceUtil.distance(moveLinkGeom));

        String moveNodeid = null;
        if (movedFirstPoint.equals(targetFirstPoint)) {
            moveNodeid = moveLink.getHllSNid();
            moveLink.setHllSNid(targetLink.getHllSNid());
        }
        if (movedFirstPoint.equals(targetLastPoint)) {
            moveNodeid = moveLink.getHllSNid();
            moveLink.setHllSNid(targetLink.getHllENid());
        }
        if (movedLastPoint.equals(targetFirstPoint)) {
            moveNodeid = moveLink.getHllENid();
            moveLink.setHllENid(targetLink.getHllSNid());
        }
        if (movedLastPoint.equals(targetLastPoint)) {
            moveNodeid = moveLink.getHllENid();
            moveLink.setHllENid(targetLink.getHllENid());
        }

        boolean updated = this.updateById(moveLink);
        OlvUtil.check(updated, Const.LINK);

        Node moveNode = this.nodeService.getById(moveNodeid);
        moveNode.setTaskId(taskId);
        moveNode.setUpDate(now);
        moveNode.setStatus(Const.STATUS_DELETED);
        moveNode.setDatasource(commonService.datasource(datasource, moveNode.getDatasource()));
        updated = this.nodeService.updateById(moveNode);
        OlvUtil.check(updated, Const.NODE);

        // 首尾相连校验
        Integer count = this.lambdaQuery()
                .and(q -> q
                        .or(i -> i.eq(Link::getHllSNid, moveLink.getHllSNid()).eq(Link::getHllENid, moveLink.getHllENid()))
                        .or(i -> i.eq(Link::getHllSNid, moveLink.getHllENid()).eq(Link::getHllENid, moveLink.getHllSNid()))
                )
                .ne(Link::getStatus, Const.STATUS_DELETED)
                .count();
        if (count > 1) throw new BizException("It is not allowed to have more than two roads connected sequentially.");

        this.updateConnectTable(moveLink.getHllLinkid(), moveNodeid, taskId, datasource);

        return true;
    }

    @DSTransactional
    public boolean connectLink(LinkConnectReq req) {
        commonService.checkReq(req);
        Link link = req.getLink();
        Link moveLink = req.getMoveLink();
        Node moveNode = req.getMoveNode();

        String hllLinkid = link.getHllLinkid();
        String taskId = req.getTaskId();
        String datasource = req.getDatasource();

        Geometry targetGeom = link.getGeometry();
        Geometry intersectionPoint = moveNode.getGeometry();

        List<Geometry> splitLineStrings = split(targetGeom, intersectionPoint);
        Geometry moveLinkGeom = moveLink.getGeometry();
        int length = targetGeom.intersection(moveLinkGeom).getCoordinates().length;
        if (length > 1) {
            throw new BizException("Failed to split: The connecting line intersects the target line at non-unique points (" + length + ").");
        }

        Point targetFirstPoint = geometryFactory.createPoint(targetGeom.getCoordinates()[0]);
        List<Long> ids = commonService.generateIds(datasource, 5);
        LocalDateTime now = LocalDateTime.now();
        Integer area = getArea(link);
        //打断，第一截
        Link firstLink = new Link();
        BeanUtils.copyProperties(link, firstLink);
        String firstHllLinkid = String.valueOf(ids.get(0));
        firstLink.setHllLinkid(firstHllLinkid);
        firstLink.setLinkId(firstHllLinkid);
        firstLink.setHllSNid(link.getHllSNid());
        firstLink.setUpDate(now);
        firstLink.setStatus(Const.STATUS_NEW);
        firstLink.setTaskId(taskId);
        firstLink.setDatasource(datasource);
        firstLink.setArea(area);

        //打断，第二截
        Link secondLink = new Link();
        BeanUtils.copyProperties(link, secondLink);
        String secondHllLinkid = String.valueOf(ids.get(1));
        secondLink.setHllLinkid(secondHllLinkid);
        secondLink.setLinkId(secondHllLinkid);
        secondLink.setHllENid(link.getHllENid());
        secondLink.setUpDate(now);
        secondLink.setStatus(Const.STATUS_NEW);
        secondLink.setTaskId(taskId);
        secondLink.setDatasource(datasource);
        secondLink.setArea(area);

        if (brief(splitLineStrings, targetFirstPoint)) {
            throw new BizException("Failed to split: The resulting road segment after splitting is too short.");
        }

        String moveNodeid = moveNode.getHllNodeid();
        for (Geometry lineString : splitLineStrings) {
            if (buffer(targetFirstPoint).intersects(lineString)) {
                firstLink.setGeometry(lineString);
                firstLink.setGeomwkt(lineString.toText());
                firstLink.setHllENid(moveNodeid);
            } else {
                secondLink.setGeometry(lineString);
                secondLink.setGeomwkt(lineString.toText());
                secondLink.setHllSNid(moveNodeid);
            }
        }

        moveNode.setUpDate(now);
        moveNode.setTaskId(taskId);
        moveNode.setStatus(Const.STATUS_UPDATED);
        moveNode.setDatasource(commonService.datasource(datasource, moveNode.getDatasource()));
        moveNode.setGeomwkt(moveNode.getGeometry().toText());
        boolean updated = this.nodeService.updateById(moveNode);
        OlvUtil.check(updated, Const.NODE);

        moveLink.setUpDate(now);
        moveLink.setTaskId(taskId);
        moveLink.setStatus(Const.STATUS_UPDATED);
        moveLink.setDatasource(commonService.datasource(datasource, moveLink.getDatasource()));
        updated = this.updateById(moveLink);
        OlvUtil.check(updated, Const.LINK);

        firstLink.setLen(DistanceUtil.distance(firstLink.getGeometry()));
        firstLink.setLinkOld(hllLinkid);
        firstLink.setLinkNew(firstLink.getHllLinkid());
        this.save(firstLink);

        secondLink.setLen(DistanceUtil.distance(secondLink.getGeometry()));
        secondLink.setLinkOld(hllLinkid);
        secondLink.setLinkNew(secondLink.getHllLinkid());
        this.save(secondLink);

        link.setUpDate(now);
        link.setTaskId(taskId);
        link.setStatus(Const.STATUS_DELETED);
        link.setDatasource(commonService.datasource(datasource, link.getDatasource()));
        updated = this.updateById(link);
        OlvUtil.check(updated, Const.LINK);

        splitConnectionTable(hllLinkid, firstLink, secondLink);
        return true;
    }

    @DSTransactional
    public List<Link> updateBatchAttribute(LinkAttributeUpdateBatchReq req) {
        commonService.checkReq(req);
        List<LinkAttributeUpdateBatchReq.LinkReq> links = req.getLinks();
        LocalDateTime now = LocalDateTime.now();
        boolean updated;
        List<Link> result = new ArrayList<>();
        for (LinkAttributeUpdateBatchReq.LinkReq link : links) {
            LambdaUpdateChainWrapper<Link> lambdaUpdate = this.lambdaUpdate();
            lambdaUpdate.eq(Link::getHllLinkid, link.getHllLinkid())
                    .eq(Link::getOlv, link.getOlv())
                    .set(Link::getKind, link.getKind())
                    .set(Link::getFormway, link.getFormway())
                    .set(Link::getDir, link.getDir())
                    .set(Link::getFunct, link.getFunct())
                    .set(Link::getApp, link.getApp())
                    .set(Link::getToll, link.getToll())
                    .set(Link::getArVeh, link.getArVeh())
                    .set(Link::getNameChO, link.getNameChO())
                    .set(Link::getOlv, link.getOlv() + 1)
                    .set(Link::getUpDate, now)
                    .set(Link::getStatus, Const.STATUS_UPDATED)
                    .set(Link::getTaskId, req.getTaskId())
                    .set(Link::getDatasource, commonService.datasource(req.getDatasource(), link.getDatasource()));
            updated = lambdaUpdate.update();
            result.add(this.getById(link.getHllLinkid()));
            OlvUtil.check(updated, Const.LINK);
        }

        return result;
    }

    @DSTransactional
    public boolean updateShape(LinkShapeUpdateReq req) {
        commonService.checkReq(req);
        List<LinkShapeUpdateReq.LinkReq> links = req.getLinks();
        LambdaUpdateChainWrapper<Link> lambdaUpdate;
        LocalDateTime now = LocalDateTime.now();
        boolean updated;
        for (LinkShapeUpdateReq.LinkReq link : links) {
            lambdaUpdate = this.lambdaUpdate();
            lambdaUpdate.eq(Link::getHllLinkid, link.getHllLinkid())
                    .eq(Link::getOlv, link.getOlv())
                    .set(Link::getGeometry, link.getGeom())
                    .set(Link::getGeomwkt, link.getGeom().toText())
                    .set(Link::getLen, DistanceUtil.distance(link.getGeom()))
                    .set(Link::getOlv, link.getOlv() + 1)
                    .set(Link::getUpDate, now)
                    .set(Link::getStatus, Const.STATUS_UPDATED)
                    .set(Link::getTaskId, req.getTaskId())
                    .set(Link::getDatasource, commonService.datasource(req.getDatasource(), link.getDatasource()));
            updated = lambdaUpdate.update();
            OlvUtil.check(updated, Const.LINK);
        }

        return true;
    }

    @DSTransactional
    public Link check(String hllLinkid) {
        QueryWrapper<Link> wrapper = new QueryWrapper<>();
        wrapper.eq("hll_linkid", String.valueOf(hllLinkid));
        wrapper.ne("status", Const.STATUS_DELETED);
        Link link = this.getBaseMapper().selectOne(wrapper);
        OlvUtil.check(link != null, Const.LINK);

        Long olv = link.getOlv();
        UpdateWrapper<Link> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("hll_linkid", String.valueOf(hllLinkid));
        updateWrapper.ne("status", Const.STATUS_DELETED);
        updateWrapper.eq("olv", olv);
        updateWrapper.set("olv", olv);
        boolean updated = this.update(updateWrapper);
        OlvUtil.check(updated, Const.LINK);
        return link;
    }

    @DSTransactional
    public Map<String, Link> check(List<String> linkids, String layer, String id) {
        Map<String, Link> linkMap = new HashMap<>();
        try {
            Link link;
            for (String linkid : linkids) {
                link = this.check(linkid);
                linkMap.put(link.getHllLinkid(), link);
            }
        } catch (Exception e) {
            throw new BizException("Failed to split: layer=[" + layer + "], id=[" + id + "]");
        }
        return linkMap;
    }

    public Map<String, Link> check(String inlinkId, String outlinkId, String pass) {
        Map<String, Link> linkMap = new HashMap<>();
        Link link;
        if (StringUtils.isNotBlank(inlinkId)) {
            link = this.check(inlinkId);
            linkMap.put(link.getHllLinkid(), link);
        }
        if (StringUtils.isNotBlank(outlinkId)) {
            link = this.check(outlinkId);
            linkMap.put(link.getHllLinkid(), link);
        }
        if (StringUtils.isBlank(pass)) return linkMap;
        String[] linkids = pass.split("\\|");
        for (String linkid : linkids) {
            link = this.check(linkid);
            linkMap.put(link.getHllLinkid(), link);
        }
        return linkMap;
    }

    private boolean brief(List<Geometry> lineStrings, Point point) {
        return lineStrings.stream().allMatch(lineString -> buffer(point).intersects(lineString));
    }

    private List<Geometry> split(Geometry lineString, Geometry point) {
        List<Geometry> result = new LinkedList<>();
        LocationIndexedLine indexedLine = new LocationIndexedLine(lineString);
        LinearLocation start = indexedLine.indexOf(lineString.getCoordinates()[0]);
        LinearLocation end = indexedLine.indexOf(point.getCoordinate());
        Geometry geometry = indexedLine.extractLine(start, end);
        Coordinate[] coordinates = geometry.getCoordinates();
        Coordinate[] formatCoordinates = new Coordinate[coordinates.length];
        Coordinate formatCoordinate = new Coordinate();
        for (int i = 0; i < coordinates.length; i++) {
            formatCoordinate.x = coordinates[i].x;
            formatCoordinate.y = coordinates[i].y;
            formatCoordinates[i] = formatCoordinate;
            formatCoordinate = new Coordinate();
        }
        result.add(geometryFactory.createLineString(formatCoordinates));
        start = indexedLine.indexOf(point.getCoordinate());
        end = indexedLine.indexOf(lineString.getCoordinates()[lineString.getCoordinates().length - 1]);
        geometry = indexedLine.extractLine(start, end);
        coordinates = geometry.getCoordinates();
        formatCoordinates = new Coordinate[coordinates.length];
        formatCoordinate = new Coordinate();
        for (int i = 0; i < coordinates.length; i++) {
            formatCoordinate.x = coordinates[i].x;
            formatCoordinate.y = coordinates[i].y;
            formatCoordinates[i] = formatCoordinate;
            formatCoordinate = new Coordinate();
        }
        result.add(geometryFactory.createLineString(formatCoordinates));
        return result;
    }

    private Geometry buffer(Point sourceFirstPoint) {
        return sourceFirstPoint.buffer(0.00001);
    }

    public List<String> connectedNodes(List<Link> links) {
        Map<String, List<String>> linkMap = links.stream().collect(Collectors.toMap(Link::getHllLinkid, link -> CollUtil.toList(link.getHllSNid(), link.getHllENid())));
        // 收集点
        List<String> nodeIds = linkMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        List<String> undeleteNodeIds = new ArrayList<>();
        Set<String> originLinkIds = links.stream().map(Link::getHllLinkid).collect(Collectors.toSet());
        for (String nodeId : nodeIds) {
            List<Link> connectedNodes = this.lambdaQuery().select(Link::getHllLinkid)
                    .ne(Link::getStatus, Const.STATUS_DELETED)
                    .and(q -> q.eq(Link::getHllSNid, nodeId)
                            .or().eq(Link::getHllENid, nodeId)).list();
            List<String> linkIds = connectedNodes.stream().map(Link::getHllLinkid).collect(Collectors.toList());
            if (!originLinkIds.containsAll(linkIds)) {
                undeleteNodeIds.add(nodeId);
            }
        }
        return CollUtil.subtractToList(nodeIds, undeleteNodeIds);
    }

    public void splitConnectionTable(String hllLinkid, Link link1, Link link2) {
        this.ruleService.split(hllLinkid, link1, link2);
        this.relationService.split(hllLinkid, link1, link2);
        this.zlevelService.split(hllLinkid, link1, link2);
    }

    public void deleteConnectionTable(String hllLinkid, String taskId, String datasource) {
        this.ruleService.update(hllLinkid, taskId, datasource, Const.STATUS_DELETED);
        this.relationService.update(hllLinkid, taskId, datasource, Const.STATUS_DELETED);
        this.zlevelService.update(hllLinkid, taskId, datasource, Const.STATUS_DELETED);
    }

    public void updateConnectionTable(String hllLinkid, String taskId, String datasource) {
        this.ruleService.update(hllLinkid, taskId, datasource, Const.STATUS_UPDATED);
        this.relationService.update(hllLinkid, taskId, datasource, Const.STATUS_UPDATED);
        this.zlevelService.update(hllLinkid, taskId, datasource, Const.STATUS_UPDATED);
    }

    public void updateConnectTable(String hllLinkid, String hllNodeid, String taskId, String datasource) {
        this.ruleService.update4ConnectNode(hllNodeid, taskId, datasource);
        this.relationService.update4ConnectNode(hllNodeid, taskId, datasource);
        this.zlevelService.update(hllLinkid, taskId, datasource, Const.STATUS_UPDATED);
    }

    public boolean needNewNodeid(String newNodeid) {
        return StrUtil.isBlank(newNodeid) ||
               this.nodeService.lambdaQuery().eq(Node::getHllNodeid, newNodeid).count() > 0;
    }

    public Map<String, Link> queryMapByLinkids(List<String> hllLinkids) {
        List<List<String>> partitions = Lists.partition(hllLinkids, 2000);
        Map<String, Link> linkMap = new HashMap<>();
        List<Link> links;
        for (List<String> partition : partitions) {
            links = this.lambdaQuery().in(Link::getHllLinkid, partition).list();
            links.forEach(link -> linkMap.put(link.getHllLinkid(), link));
        }
        return linkMap;
    }

    public Map<String, Link> queryMapByExtent(String extent) {
        List<Link> links = this.queryByExtent(extent, "0,1,2,3");
        if (CollectionUtils.isEmpty(links)) return null;

        return links.stream().collect(Collectors.toMap(Link::getHllLinkid, link -> link));
    }
}
