<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>work.llm.map</groupId>
        <artifactId>map-global-road-editor-svc</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>map-global-road-editor-svc-service</artifactId>

    <properties>
        <road-task.version>1.0.0-SNAPSHOT</road-task.version>
        <lock4j.redisson.version>2.2.7</lock4j.redisson.version>
        <lock4j.redis-template.version>2.2.7</lock4j.redis-template.version>
        <redisson.version>3.32.0</redisson.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>work.llm.map</groupId>
            <artifactId>map-global-road-editor-svc-dao</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>work.llm.map</groupId>
            <artifactId>map-global-road-editor-svc-integration</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.lalaframework.boot</groupId>
            <artifactId>lala-boot-starter-config</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.lalaframework</groupId>
            <artifactId>lala-jaf-monitor-starter</artifactId>
        </dependency>

        <!-- jaf soa dependency start -->
        <dependency>
            <groupId>work.llm.map</groupId>
            <artifactId>map-global-road-editor-svc-facade</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- jaf soa dependency stop -->

        <!-- jaf dynamic dependency start -->
        <dependency>
            <groupId>cn.lalaframework.boot</groupId>
            <artifactId>lala-boot-starter-dynamic</artifactId>
        </dependency>
        <!-- jaf dynamic dependency stop -->
        <!-- task interfaces -->
        <dependency>
            <groupId>work.llm.map</groupId>
            <artifactId>map-global-road-task-facade</artifactId>
            <version>${road-task.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.lalaframework</groupId>
            <artifactId>lala-xxl-job-starter</artifactId>
        </dependency>
    </dependencies>
</project>
