package work.llm.map.start;

import cn.lalaframework.launcher.LalaApplication;
import cn.lalaframework.utils.ConfigUtil;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = "work.llm.map.*")
@MapperScan("work.llm.map.dao.*.entity")
public class ApplicationStarter {

	public static void main(String[] args) {
		LalaApplication.run(ConfigUtil.getAppId(), ApplicationStarter.class, args);
	}
}
