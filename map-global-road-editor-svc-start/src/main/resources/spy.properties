# p6spy configuration
# specifies the appender to use for logging
appender=com.p6spy.engine.spy.appender.Slf4JLogger
# specifies the log file to write to
#logfile=spy.log
# specifies the class to use for formatting log messages
logMessageFormat=com.p6spy.engine.spy.appender.CustomLineFormat
# Custom log format
customLogMessageFormat=%(currentTime)|%(executionTime)|%(category)|connection%(connectionId)|%(sqlSingleLine)
# sets the date format using Java's SimpleDateFormat available formats
dateformat=yyyy-MM-dd HH:mm:ss
# whether to expose the p6spy driver to your application
driverlist=org.postgresql.Driver
# comma separated list of tables to include
#include=
# comma separated list of tables to exclude
#exclude=
# controls whether or not log contains stack trace
stacktrace=false
# determines if property file should be reloaded
reloadproperties=false
# determines how often to reload the properties file (in seconds)
reloadpropertiesinterval=60
# enables module loading
modulelist=com.p6spy.engine.spy.P6SpyFactory,com.p6spy.engine.logging.P6LogFactory
# prints a stack trace for every statement logged
stacktraceclass=
# whether to append to the p6spy log file
append=true
# whether to log the execution time
executionThreshold=0
# whether to include non-SQL statements
excludecategories=info,debug,result,batch,resultset
# whether to include SQL statements
includecategories=
# controls logging of the setup of the logging system
excludebinary=true
# whether to log batch statements
outagedetection=false
# number of seconds a query must run before it is considered an outage
outagedetectioninterval=2
# function to call when an outage occurs
#outagedetectioninterval=
# whether to filter out single-line comments (--) before sending statements to the database
filter=false