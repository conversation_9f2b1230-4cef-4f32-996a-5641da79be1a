spring:
  profiles:
    active: stg
  main:
    allow-bean-definition-overriding: true

apollo:
  bootstrap:
    namespaces: application

#配置mybatis
mybatis-plus:
  mapper-locations: classpath*:mybatis/mapper/*.xml
  type-aliases-package: work.llm.map.dao.**.entity*
  type-aliases-super-type: cn.lalaframework.orm.mybatis.entity.PO
  type-handlers-package: work.llm.map.dao.common
  global-config:
    db-config:
      update-strategy: ignored
  configuration:
    lazy-loading-enabled: false
    map-underscore-to-camel-case: true
    default-fetch-size: 100
    default-statement-timeout: 3

#配置应用端口
server:
  port: 9000
  servlet:
    context-path: /api/oversea/road/editor

#lala相关配置
lala:
  soa:
    provider:
      scan-base-packages: work.llm.map.provider

properties:
  area-interceptor:
    header-key-area: x-oversea-area
    ignore-uris:
      - /springdoc/**
      - /area/**
      - /swagger-ui/**
      - /v3/api-docs/**

# SpringDoc配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: work.llm.map.api
lock4j:
  expire: 86400000
  acquire-timeout: 1
logging:
  level:
    default: debug
    p6spy: info
    com.p6spy: info

# P6Spy configuration
decorator:
  datasource:
    p6spy:
      enable-logging: true
      multiline: true
      logging: slf4j