spring:
  datasource:
    dynamic:
      primary: default
      datasource:
        default:
          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          url: ********************************************************************
          username: postgres
          password: <PERSON><PERSON><PERSON>@2021
        mys:
          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          url: *********************************************************************
          username: postgres
          password: <PERSON><PERSON><PERSON>@2021
        phl:
          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          url: *********************************************************************
          username: postgres
          password: <PERSON><PERSON><PERSON>@2021
        sgp:
          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          url: *********************************************************************
          username: postgres
          password: <PERSON><PERSON><PERSON>@2021
        hkg:
          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          url: *********************************************************************
          username: postgres
          password: Huolala@2021
        idn:
          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          url: *********************************************************************
          username: postgres
          password: Huolala@2021
        twn:
          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          url: *********************************************************************
          username: postgres
          password: Huolala@2021
