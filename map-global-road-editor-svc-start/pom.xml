<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>work.llm.map</groupId>
        <artifactId>map-global-road-editor-svc</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>map-global-road-editor-svc-start</artifactId>

    <dependencies>

        <dependency>
            <groupId>work.llm.map</groupId>
            <artifactId>map-global-road-editor-svc-controller</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>work.llm.map</groupId>
            <artifactId>map-global-road-editor-svc-provider</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- lala-launcher -->
        <dependency>
            <groupId>cn.lalaframework</groupId>
            <artifactId>lala-launcher</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.gavlyukovskiy</groupId>
            <artifactId>p6spy-spring-boot-starter</artifactId>
            <version>1.10.0</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>map-global-road-editor-svc</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
