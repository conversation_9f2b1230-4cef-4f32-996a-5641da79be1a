package work.llm.map.provider;

import cn.huolala.arch.hermes.api.annotation.HermesService;
import org.springframework.stereotype.Service;
import work.llm.map.common.utils.MapperUtils;
import work.llm.map.facade.model.TaskCreateDTO;
import work.llm.map.facade.service.RoadEditor4TaskFacade;
import work.llm.map.service.task.TaskService;
import work.llm.map.service.task.model.req.TaskCreateReq;

import javax.annotation.Resource;

@Service
@HermesService
public class RoadEditor4Task4TaskProvider implements RoadEditor4TaskFacade {

    @Resource
    private TaskService taskService;

    @Override
    public boolean createTask(TaskCreateDTO taskCreateDTO) {
        TaskCreateReq taskCreateReq = MapperUtils.map(taskCreateDTO, TaskCreateReq.class);
        return taskService.create(taskCreateReq);
    }
}
