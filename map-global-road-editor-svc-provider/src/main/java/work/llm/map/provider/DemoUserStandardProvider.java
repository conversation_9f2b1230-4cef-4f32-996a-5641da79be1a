package work.llm.map.provider;

import cn.huolala.arch.hermes.api.annotation.HermesService;
import work.llm.map.common.utils.MapperUtils;
import work.llm.map.facade.model.UserDTO;
import work.llm.map.facade.model.UserPage;
import work.llm.map.facade.service.DemoUserStandardFacade;
import work.llm.map.service.user.UserService;
import work.llm.map.service.user.UserInfoService;
import work.llm.map.service.user.model.UserDO;
import work.llm.map.service.user.model.UserInfoDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@HermesService
public class DemoUserStandardProvider implements DemoUserStandardFacade {

	@Resource
	private UserService userService;

	@Resource
	private UserInfoService userInfoService;


	/**
	 * 创建用户
	 *
	 * @param userDTO 用户信息
	 */
	@Override
	public void createUser(UserDTO userDTO) {
		UserDO userDo = MapperUtils.map(userDTO, UserDO.class);
		userService.createUser(userDo);
	}

	/**
	 * 根据id查找
	 *
	 * @param id id
	 * @return {@link UserDTO}
	 */
	@Override
	public UserDTO getById(Long id) {
		UserDO userDo = userService.getById(id);
		return MapperUtils.map(userDo, UserDTO.class);
	}

	/**
	 * 根据名称查找用户集合
	 *
	 * @param name 名称
	 * @return {@link List <UserPO>}
	 */
	@Override
	public List<UserDTO> listByName(String name) {
		List<UserDO> users = new ArrayList<>();
		return MapperUtils.mapList(users,UserDTO.class);
	}

	@Override
	public UserPage getUserPage(int page, int pageSize) {
		UserPage userPage = new UserPage();
		userPage.setTotal(userInfoService.getPageCount());
		List<UserInfoDO> userInfoDOList = userInfoService.getPageList(page, pageSize);
		userPage.setUserList(
				userInfoDOList.stream().map(userInfoPo -> MapperUtils.map(userInfoPo, UserDTO.class)).collect(Collectors.toList()));
		return userPage;
	}
}
